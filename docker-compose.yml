version: '3'

services:
  easygochina:
    container_name: easygochina
    image: qutke/easygo:dev # git
    ports:
      - 7006:3000
    networks:
      - easygo
    restart: always
    depends_on:
      - mongo
    env_file:
      - .env.prod

  mongo:
    image: mongo:5.0.18
    container_name: mongo
    restart: always
    ports:
      - 27019:27017
    networks:
      - easygo
    command: mongod --keyFile /data/mongodb.key
    environment:
      - MONGO_INITDB_ROOT_USERNAME=easygochina
      - MONGO_INITDB_ROOT_PASSWORD=jdskd232djsdDs3
    volumes:
      - /data/easygo/docker/mongodb/data/db:/data/db
    entrypoint:
      - bash
      - -c
      - |
        openssl rand -base64 128 > /data/mongodb.key
        chmod 400 /data/mongodb.key
        chown 999:999 /data/mongodb.key
        # 启动MongoDB服务
        exec docker-entrypoint.sh "$$@" &

        # 等待MongoDB服务启动
        until mongo -u easygochina -p jdskd232djsdDs3 --authenticationDatabase admin --eval "print('waited for connection')" > /dev/null 2>&1; do
          echo "Waiting for MongoDB to start..."
          sleep 2
        done
        # 等待docker-entrypoint.sh脚本执行的MongoDB服务进程
        wait $$!

volumes:
  data:
  node_modules:

networks:
  easygo: