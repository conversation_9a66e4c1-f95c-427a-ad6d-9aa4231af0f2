{"name": "@easygo/website", "version": "1.0.0", "description": "Website for EasyGo", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "dev:prod": "cross-env NODE_OPTIONS=--no-deprecation rm -rf .next && pnpm build && pnpm start", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "ii": "cross-env NODE_OPTIONS=--no-deprecation pnpm --ignore-workspace install", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "lint:fix": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "reinstall": "cross-env NODE_OPTIONS=--no-deprecation rm -rf node_modules && rm pnpm-lock.yaml && pnpm --ignore-workspace install", "start": "cross-env NODE_OPTIONS=--no-deprecation next start"}, "dependencies": {"@aws-sdk/client-s3": "^3.726.1", "@aws-sdk/node-http-handler": "^3.374.0", "@aws-sdk/s3-request-presigner": "^3.726.1", "@payload-enchants/translator": "^1.3.0", "@payloadcms/db-mongodb": "^3.15.1", "@payloadcms/email-resend": "^3.15.1", "@payloadcms/live-preview-react": "^3.15.1", "@payloadcms/next": "^3.15.1", "@payloadcms/payload-cloud": "^3.15.1", "@payloadcms/plugin-form-builder": "^3.15.1", "@payloadcms/plugin-nested-docs": "^3.15.1", "@payloadcms/plugin-redirects": "^3.15.1", "@payloadcms/plugin-search": "^3.15.1", "@payloadcms/plugin-seo": "^3.15.1", "@payloadcms/plugin-stripe": "3.15.1", "@payloadcms/richtext-lexical": "^3.15.1", "@payloadcms/storage-s3": "^3.15.1", "@payloadcms/translations": "^3.15.1", "@payloadcms/ui": "^3.15.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cross-env": "^7.0.3", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "geist": "^1.3.0", "graphql": "^16.8.2", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.378.0", "next": "^15.1.0", "next-sitemap": "^4.2.3", "payload": "^3.15.1", "payload-admin-bar": "^1.0.6", "payload-oapi": "^0.2.3", "prism-react-renderer": "^2.3.1", "react": "^19.0.0", "react-confetti": "^6.2.2", "react-day-picker": "^9.5.0", "react-dom": "^19.0.0", "react-hook-form": "7.45.4", "react-resizable-panels": "^2.1.7", "react-use": "^17.6.0", "recharts": "^2.15.0", "sharp": "0.32.6", "sonner": "^1.7.2", "stripe": "^17.6.0", "styled-components": "^6.1.14", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "validator": "^13.12.0", "vaul": "^1.1.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@tailwindcss/typography": "^0.5.13", "@types/escape-html": "^1.0.2", "@types/node": "22.5.4", "@types/react": "19.0.1", "@types/react-dom": "19.0.1", "autoprefixer": "^10.4.19", "copyfiles": "^2.4.1", "eslint": "^9.16.0", "eslint-config-next": "15.1.0", "postcss": "^8.4.38", "prettier": "^3.4.2", "tailwindcss": "^3.4.3", "typescript": "5.7.2"}, "engines": {"node": "^18.20.2 || >=20.9.0"}, "pnpm": {"onlyBuiltDependencies": ["esbuild", "sharp"]}}