#!/bin/bash

# 设置错误时退出
set -e

# 定义目录
PROD_DIR=".next"
BUILD_DIR="/data/apps/builds"
APP_DIR="/data/apps"
CODE_TAR_FILE="/data/apps/easygo/easygo.tar.gz"

echo "开始构建..."

cd $BUILD_DIR

# 复制代码之前将 app.tar.gz 被分为 app.tar.gz+日期
if [ -f easygo.tar.gz ]; then
  BACKUP_FILE="easygo.tar.gz.$(date +%Y%m%d_%H%M%S)"
  mv easygo.tar.gz "backups/$BACKUP_FILE"
  echo "已备份代码文件到: $BACKUP_FILE"
fi

cp $CODE_TAR_FILE .

tar -zxvf easygo.tar.gz

cd website

# 执行构建
if ! pnpm build; then
  echo "构建失败"
  exit 1
fi

echo "构建完成，准备替换目录..."


# 如果生产目录存在，删除它
if [ -d "$APP_DIR/website/$PROD_DIR" ]; then
  rm -rf "$APP_DIR/website/$PROD_DIR"
fi

# 将临时目录重命名为生产目录
mv "$PROD_DIR" "$APP_DIR/website/$PROD_DIR"

cd ..

tar xzvf easygo.tar.gz -C $APP_DIR

pm2 restart easygo

echo "部署成功完成"