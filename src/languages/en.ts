const en = {
  authentication: {
    emailHasNotBeenRegistered: '<PERSON>ail has not been registered',
    invalidOrExpired: '{{token}} is either invalid or has expired',
    otpCode: 'OTP Code',
    passwordIncorrect: 'Password incorrect',
    passwordsNotMatch: 'Password and confirm password do not match',
    stillValid: '{{token}} is still valid',
  },
  collection: {
    cart: 'Cart',
    itinerary: 'Itinerary',
    product: 'Product',
    redemptionCode: 'Redemption Code',
    user: 'User',
    invitationCode: 'Invitation Code',
    voucher: 'Voucher',
  },
  error: {
    alreadyInCart: 'Already in cart',
    alreadyPurchasedOrRedeemed: 'Already purchased or redeemed',
    canNotReuse: 'Can not reuse',
    notFound: '{{resource}} not found',
    usageLimitReached: '{{resource}} usage limit reached',
  },
  general: {
    accountCancelled: 'Account Cancelled',
    addToCartSuccessfully: 'Added to cart successfully',
    payNow: 'Pay now',
    redeemSuccessfully: 'Redeemed successfully',
    defaultVoucherName: 'Universal Tour Voucher',
  },
  landing: {
    header: {
      features: 'Features',
      reviews: 'Reviews',
      story: 'Our Story',
      contact: 'Contact',
      download: 'Download App',
      origin: 'Original Site',
    },
    hero: {
      title: 'Your Ultimate',
      title2: 'Self-Guided',
      title3: 'Travel Companion',
      title4: 'in China',
      subtitle:
        'Explore China freely with curated routes, local food, and cultural stories – all in one app.',
    },
    valueProposition: {
      sectionTitle: 'Why Choose EasyGo China?',
      sectionDescription:
        'Experience China like never before with our comprehensive travel companion',
      features: [
        {
          title: 'No Chinese Required',
          description:
            'Built-in navigation with multilingual explanations for local attractions. Navigate attractions, food, and transportation effortlessly without knowing Chinese.',
        },
        {
          title: 'Authentic Food, Smart Spending',
          description:
            'High-value food guide with genuine local recommendations. Skip tourist traps and enjoy three authentic meals for the price of one tourist group dinner.',
        },
        {
          title: 'Stories Behind Every Stop',
          description:
            'More than just check-ins - deep cultural experiences. From imperial streets to hutong cafes, discover cultural surprises in every corner.',
        },
        {
          title: 'One-Click Purchase, Instant Use',
          description:
            'No groups, no rushing - control your own journey. Buy and use immediately, perfect for spontaneous trips and true independent travel.',
        },
        {
          title: 'Only $9.99/Day - Affordable Excellence',
          description:
            'Save $70+ compared to group tours while gaining more freedom and authentic experiences. No hidden costs, avoid tourist traps.',
        },
      ],
    },
    userTestimonials: {
      sectionTitle: 'Loved by Travelers Around the World',
      sectionDescription:
        'Hear real stories from travelers who experienced China with EasyGo China',
      sectionImageTitle: 'Real Stories from Our Travelers',
      sectionImageDescription: 'Discover how EasyGo China transformed their travel experience',
      sectionTitle2: 'Trusted by users from 20+ countries',
      sectionDescription2: 'Join thousands of satisfied travelers worldwide ',
      testimonials: [
        {
          text: 'EasyGo China made my trip absolutely seamless. The translation feature saved me countless times!',
          country: 'USA',
        },
        {
          text: 'The local recommendations were spot-on. I discovered hidden gems I never would have found otherwise.',
          country: 'UK',
        },
        {
          text: "Best travel app I've ever used. The cultural insights helped me connect with locals authentically.",
          country: 'Spain',
        },
        {
          text: 'An AI assistant can promptly translate and interpret some additional common knowledge questions.',
          country: 'Singapore',
        },
        {
          text: 'The built-in navigation made exploring totally hassle-free — super easy and convenient!',
          country: 'Canada',
        },
        {
          text: "Found the most amazing local restaurants that weren't in any guidebook. This app is a game-changer!",
          country: 'Australia',
        },
      ],
      videoTitle: 'Real Stories from Our Travelers',
      videoDescription: 'Hear how real users rate their EasyGo China travel experience',
    },
    ourStory: {
      title: 'Our Story',
      story1:
        'In the summer of 2024, my wife and I had to cancel plans to host our good friend Janet from Chicago during her trip to Beijing. To make up for it, we wrote her a little travel guide.',
      story11:
        'Well… it started as &quot;just a little something&quot; to help her have a good time — but before we knew it, we&apos;d put together a full-blown handbook. It included everything from must-see sights and cultural tips to transport guides and daily food recommendations.',
      story2:
        'We didn&apos;t want her to worry about meals, so we listed 7–8 restaurants per day, each with 5–7 recommended dishes. We even included ingredients, cooking methods, flavor profiles, and how to eat them — just in case she wasn&apos;t sure what she&apos;d like. We threw in dozens of cafes and bars too.',
      story21:
        '&quot;This is better than a tour guide — it&apos;s like you&apos;re right here with me!&quot;',
      story22: 'Janet, after using our guide for just one day',
      story3:
        'That moment made us realize: after 20+ years of travel across 30+ countries, maybe we could help more friends like Janet.',
      story31:
        'So we spent the next two months building a website and invited Reddit users to test it out. The feedback blew us away:',
      story32:
        '&quot;It&apos;s way more accurate than what I found online — saved me hours of research!&quot;',
      and: 'and',
      story33: '&quot;Beyond dumplings and roast duck, I found so many hidden food gems!&quot;',
      story4:
        'Of course, people also mentioned some issues — slow loading, confusing maps, and not enough English support. That&apos;s why we created the EasyGo China app.',
      story41:
        'It&apos;s like having a super-knowledgeable local friend in your pocket — at less than the price of a cocktail.',
      featureMap: {
        feature1Title: 'Multi-Language Friendly',
        feature1Description: 'Built-in language support system for travelers worldwide',
        feature2Title: '30+ Self-Guided Tours',
        feature2Description: 'Covering 11 cities across China',
        feature3Title: '20,000+ Words',
        feature3Description: 'Of content per day (some hit 40,000!)',
      },
      statement: {
        title: 'Here&apos;s what we want every traveler in China to enjoy:',
        title1: 'No Chinese? No problem.',
        description1:
          'Easily find dishes you&apos;ll love — even if they have wild names like &quot;Ants Climbing a Tree&quot; or &quot;Husband &amp; Wife Lung Slices.&quot;',
        title2: 'No tour groups needed.',
        description2:
          'Discover local favorites like foot massages, ear-cleaning, teahouses, and hidden bars — just like the locals do.',
        title3: 'No hours of Googling.',
        description3: 'Just open the app and feel like a friend is showing you around.',
      },
      message: {
        title:
          'After 20 years of DIY travel experience, we built EasyGo China to help you experience the real heartbeat of this amazing country — because the best trips aren&apos;t just about checking off sights.',
        description: 'They&apos;re about truly understanding the soul of a city.',
      },
    },
    use: {
      title: 'How to Use EasyGo China',
      description: 'Simple steps to unlock the best of China.',
      description2: 'From planning to exploring, we&apos;ve got you covered.',
    },
    contact: {
      title: 'Stay Connected',
      description: 'Follow us for travel tips, updates, and connect with fellow travelers',
      socialLinks: [
        {
          description: 'Chat with us on WeChat',
        },
        {
          description: 'Follow us on TikTok',
        },
        {
          description: 'Follow our travel stories',
        },
        {
          description: 'Watch travel guides',
        },
        {
          description: 'Join our community',
        },
        {
          description: 'Follow us on X',
        },
      ],
      code: {
        open: 'Click to view QR code',
        scan: 'Scan QR code to connect',
      },
    },
    freeTrial: {
      title: 'Travel Like a Local',
      free: 'FREE',
      description: 'Self-Guided Tour for New Users',
      price: 'Worth $15.99',
      content:
        'Your redemption code will be sent to your registered email address immediately after registration.',
      section: 'What You Get:',
      title1: 'No Language Barriers',
      description1: 'Multilingual system lets you explore China with ease.',
      title2: 'Local Food Discoveries',
      description2: '30+ authentic restaurants with ordering guides.',
      title3: 'Cultural Insights',
      description3: '20,000+ words of cultural stories and tips.',
      title4: 'Flexible Itineraries',
      description4:
        'Play instantly. Pick your favorite spots from the self-guided tour and explore freely.',
      download: 'Download Now & Start Exploring',
      downloadText: 'Available on all platforms',
      card: 'No credit card required',
      cardText: 'Instant access',
      info: 'Offer valid for new users only. Terms and conditions apply.',
      registerSuccess:
        'You’ve successfully registered! Please download the app and log in to start using it. Wishing you an unforgettable journey in China with EasyGo China.',
    },
    finalDownload: {
      title: 'Ready to Explore China?',
      description: 'Download EasyGo China and start your adventure today',
      city: 'Cities Available',
      daily: 'Daily Itineraries',
      country: 'Countries Trust Us',
      info: 'Join thousands of travelers discovering the real China',
    },
  },
  footer: {
    subscribe: {
      info: 'The app that’s redefining travel in China.',
      title: 'Subscribe to our newsletter',
      placeholder: 'Please enter your email',
      button: 'Subscribe',
      error: 'Something went wrong',
      errorInfo: 'Please try again later.',
      success: 'Success!',
      successInfo: 'Thank you for subscribing!',
    },
  },
} as const

export default en
