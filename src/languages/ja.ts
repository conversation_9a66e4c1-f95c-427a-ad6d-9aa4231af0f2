import { title } from 'process'

const en = {
  authentication: {
    emailHasNotBeenRegistered: 'Email has not been registered',
    invalidOrExpired: '{{token}} is either invalid or has expired',
    otpCode: 'OTP Code',
    passwordsNotMatch: 'Passwords do not match',
    stillValid: '{{token}} is still valid',
  },
  collection: {
    cart: 'Cart',
    itinerary: 'Itinerary',
    product: 'Product',
    redemptionCode: 'Redemption Code',
    user: 'User',
    invitationCode: 'Invitation Code',
    voucher: 'Voucher',
  },
  error: {
    alreadyInCart: 'Already in cart',
    alreadyPurchasedOrRedeemed: 'Already purchased or redeemed',
    canNotReuse: 'Can not reuse',
    notFound: '{{resource}} not found',
    usageLimitReached: '{{resource}} usage limit reached',
  },
  general: {
    accountCancelled: 'Account Cancelled',
    addToCartSuccessfully: 'Added to cart successfully',
    payNow: 'Pay now',
    redeemSuccessfully: 'Redeemed successfully',
    defaultVoucherName: 'Universal Tour Voucher',
  },
  landing: {
    header: {
      features: '機能',
      reviews: 'コメント',
      story: '私たちの物語',
      contact: '連絡',
      download: 'ダウンロードアプリケーション',
      origin: '原駅',
    },
    hero: {
      title: 'あなたの究極',
      title2: 'セルフサービス',
      title3: '旅のパートナー',
      title4: '中国にいる',
      subtitle:
        '綿密に計画されたルート,地元の美食,文化の物語を頼りに,中国を自由に探索する--これらはすべて1つの応用にある。',
    },
    valueProposition: {
      sectionTitle: 'なぜEasyGo Chinaを選んだのですか。',
      sectionDescription:
        '私たちの全方位的な旅行付き添いサービスを利用して,今までにない中国体験の旅を始めます。',
      features: [
        {
          title: '中国語は必要ありません',
          description:
            '内蔵ナビゲーションシステムには,現地の観光地の多言語解説が付いています。中国語を理解する必要はなく,観光地,グルメ,交通を簡単にナビゲートすることができます。',
        },
        {
          title: '本場の美食, 賢明な消費',
          description:
            '高価値グルメガイドは本場のローカル推薦を提供します。ぼったくりの罠を避け,ツアーの食事1食の価格で本場の食事を3食楽しむ。',
        },
        {
          title: 'すべての駅の背後にある物語',
          description:
            'カードを打つだけではありません--より深い文化体験があります。ロイヤル街道から路地カフェまで,隅々でカルチャーサプライズに出会う。',
        },
        {
          title: 'ワンクリックで購入すれば, すぐに購入できます',
          description:
            'ツアーにつく必要はありません。急ぐ必要はありません。全行程を自主的にコントロールします。買っても使っても,歩いても歩いてもいい旅行と本当の独立した旅行にぴったりです。',
        },
        {
          title: '1 日9 .99 ドル - お得な選択, 卓越した体験',
          description:
            'ツアーに比べて70ドル以上節約し,自由と本場の体験をより多く収穫します。隠れ消費がなく,ぼったくりの罠を避ける。',
        },
      ],
    },
    userTestimonials: {
      sectionTitle: '世界中の旅行者に愛されている',
      sectionDescription: 'EasyGo Chinaを通じて中国を体験した旅行者の実話を聞く',
      sectionImageTitle: '私たち旅行者の実話',
      sectionImageDescription: 'EasyGo Chinaがどのように彼らの旅行体験を変えたかを発見した',
      sectionTitle2: '20 カ国以上のユーザーから信頼されている',
      sectionDescription2: '世界中の何千人もの満足できる旅行者の仲間入り',
      testimonials: [
        {
          text: 'EasyGo Chinaは私の旅行を非常にスムーズにしてくれました。翻訳機能は何度も助けてくれました！',
          country: 'アメリカ',
        },
        {
          text: '地元の推薦は非常に適切です。そうでなければ永遠に見つからない隠し宝を見つけた。',
          country: 'イギリス',
        },
        {
          text: '私が使った最高の旅行アプリ。文化的洞察は私が本当に地元の人と連絡を取るのを助けてくれた。',
          country: 'スペイン',
        },
        {
          text: '人工知能アシスタントは,追加の常識的な問題を迅速に翻訳し,解読することができます。',
          country: 'シンガポール',
        },
        {
          text: '内蔵ナビゲーションで探索が悩みなくなる--超簡単で便利！',
          country: 'カナダ',
        },
        {
          text: '最高のローカルレストランを見つけました。これらのレストランはどのガイドブックにも見つかりませんでした。このアプリは本当に大きな変化をもたらすことができます！',
          country: 'オーストラリア',
        },
      ],
      VideoTitle: '私たち旅行者の実話',
      VideoDescription: 'EasyGo Chinaを使った旅行体験をリアルユーザーがどのように評価するかを聞く',
    },
    ourStory: {
      title: '私たちの物語',
      story1:
        '2024年の夏,私と妻は私たちの親友でシカゴから来たジャネットが北京旅行に行っている間に彼女を招待する予定だったが,その計画をキャンセルせざるを得なかった。補うために,私たちは彼女に小さな旅行ガイドを書いた。',
      story11:
        '実は……最初は 小物 を作って楽しんであげたいだけだったのですが,いつの間にか,私たちは内容の詳細なマニュアルを整理していました。必須観光地,文化的なパンフレットから,交通ガイドや日常のグルメのおすすめまで,さまざまな内容が含まれています。',
      story2:
        '私たちは彼女に食事の心配をかけたくないので,毎日7 ~ 8軒のレストランをリストアップして,どの家にも5 ~ 7つのおすすめ料理があります。私たちは各料理の食材,調理法,味の特徴,食べ方まで書いています。彼女が何が好きなのか分からないのではないかと心配しています。私たちはさらに何十軒ものカフェやバーに参加しています。',
      story21: 'ガイドよりもいいですよ。 あなたたちが私のそばにいるように!',
      story22: 'ジャネット, 私たちのガイドを1日使った後',
      story3:
        'その瞬間,私たちは20年以上の間に30以上の国を旅した後,ジャネットのような友人をもっと助けることができるかもしれないことに気づいた。',
      story31:
        'そこで,次の2ヶ月間,私たちはウェブサイトを構築し,Redditユーザーに試用を依頼しました。受け取ったフィードバックに驚きました:',
      story32:
        'これは私がネットで見つけた情報よりずっと正確です。 何時間も調べる時間を節約してくれました!”',
      and: 'と',
      story33: '餃子やダックのほかにも, 隠れたグルメの宝庫をたくさん見つけました!”',
      story4:
        'もちろん,ロードが遅い,地図が混乱している,英語のサポートが不足しているという問題にも言及されています。これがEasyGo Chinaというアプリケーションを開発した理由です。',
      story41:
        'これはポケットに博識な地元の友人が入っているようなものです。しかもカクテル1杯分のお金もありません。',
      featureMap: {
        feature1Title: '多言語フレンドリー',
        feature1Description: '世界中の旅行者のための組み込み言語サポートシステム',
        feature2Title: '30+セルフツアー',
        feature2Description: '中国の11都市をカバーする',
        feature3Title: '20,000+語',
        feature3description: '1 日あたりのコンテンツ量（ 40,000 字に達するものもある！）',
      },
      statement: {
        title: '以下は私たちが中国の旅行者一人一人が楽しむことを望んでいます:',
        title1: 'は中国語が話せませんか？大丈夫です。',
        description1:
          'アリの木 や夫婦の肺片 など, 奇妙な名前を持っていても, 好きな料理を簡単に見つけることができます。 ',
        title2: 'ツーアは必要ありません。',
        description2:
          '足裏マッサージ,耳かき,茶屋,隠れたバーなど,地元の人が好むものを発見します。地元の人のように。',
        title3: '数時間のGoogle検索は必要ありません。',
        description3: 'このアプリを開くだけで,友達があなたを連れて回っているような気がします。',
      },
      message: {
        title:
          '20年のセルフサービス旅行の経験をもとに,私たちはEasyGo Chinaを開発しました。この不思議な国の本当の脈動を感じるのを助けることを目指しています。最高の旅行はカード観光地だけではないからです。',
        description: 'それらは本当に都市の魂を理解することに関係しています。',
      },
    },
    use: {
      title: 'EasyGo Chinaの使い方',
      description: '簡単なステップで,中国の精華を開く。',
      description2: '計画から探索まで,私たちはあなたのために全方位的なサポートを提供します。',
    },
    contact: {
      title: '連絡を取り合う',
      description: '私たちに注目して, 旅行のヒント, 最新情報を取得して, 他の旅行者と連絡を取り合う',
      socialLinks: [
        {
          description: 'ウィチャットで私たちと話す',
        },
        {
          description: 'TikTokで私たちをフォローして',
        },
        {
          description: '私たちの旅の物語に注目して',
        },
        {
          description: '旅行ガイドを見る',
        },
        {
          description: '私たちのコミュニティに参加する',
        },
        {
          description: 'Xで私たちに注目して',
        },
      ],
      code: {
        open: 'クリックしてQRコードを表示する',
        scan: 'QRコードをスキャンして接続する',
      },
    },
    freeTrial: {
      title: '地元の人のように旅行する',
      free: '無料',
      description: '新規ユーザーのセルフサービスツアー',
      price: '15.99 ドルの価値がある',
      content: '交換コードは登録後すぐに登録したメールアドレスに送信されます。',
      section: '取得する:',
      title1: '言語障害なし',
      description1: '多言語システムにより,中国を簡単に探索できます。',
      title2: 'ご当地グルメ発見',
      description2: '30以上の本格的なレストランには,注文ガイドが付いています。',
      title3: '文化的洞察',
      description3: '2万字以上の文化物語と小貼り。',
      title4: 'フレキシブルストローク',
      description4:
        'はすぐに開きます。セルフサービスの中から好きなスポットを選んで,自由に探索します。',
      download: 'すぐにダウンロードして, 探索を開始します',
      downloadText: 'フルプラットフォームで使用可能',
      card: 'クレジットカード不要',
      cardText: '即時使用',
      info: '特典は新規ユーザー限定です。適用条項。',
      registerSuccess:
        '登録が完了しました！アプリをダウンロードしてログインし、利用を開始してください。EasyGo Chinaと共に中国で忘れられない旅をお楽しみください。',
    },
    finalDownload: {
      title: '中国を探索する準備はできていますか。',
      description: 'EasyGo Chinaをダウンロードして, 今日からあなたの冒険を始めましょう',
      city: '利用可能な都市',
      daily: '毎日のスケジュール',
      country: '私たちの国を信頼している',
      info: '何千人もの旅行者が参加し, 真実の中国を探索する',
    },
  },
  footer: {
    subscribe: {
      info: 'これは中国旅行のアプリケーションを再定義しています。',
      title: '購読してください',
      placeholder: 'メールボックスを入力してください',
      button: 'サブスクリプション',
      error: 'ちょっと問題があった',
      errorInfo: '後で試してください。',
      success: '成功！',
      successInfo: '購読に成功しました！購読ありがとうございます！',
    },
  },
} as const

export default en
