const en = {
  authentication: {
    emailHasNotBeenRegistered: 'Em<PERSON> has not been registered',
    invalidOrExpired: '{{token}} is either invalid or has expired',
    otpCode: 'OTP Code',
    passwordsNotMatch: 'Passwords do not match',
    stillValid: '{{token}} is still valid',
  },
  collection: {
    cart: 'Cart',
    itinerary: 'Itinerary',
    product: 'Product',
    redemptionCode: 'Redemption Code',
    user: 'User',
    invitationCode: 'Invitation Code',
    voucher: 'Voucher',
  },
  error: {
    alreadyInCart: 'Already in cart',
    alreadyPurchasedOrRedeemed: 'Already purchased or redeemed',
    canNotReuse: 'Can not reuse',
    notFound: '{{resource}} not found',
    usageLimitReached: '{{resource}} usage limit reached',
  },
  general: {
    accountCancelled: 'Account Cancelled',
    addToCartSuccessfully: 'Added to cart successfully',
    payNow: 'Pay now',
    redeemSuccessfully: 'Redeemed successfully',
    defaultVoucherName: 'Universal Tour Voucher',
  },
  landing: {
    header: {
      features: 'Функции',
      reviews: 'Отзывы',
      story: 'Наша история',
      contact: 'Связаться',
      download: 'Скачать приложение',
      origin: 'Оригинальный сайт',
    },
    hero: {
      title: 'Твой окончательный',
      title2: 'самостоятельный',
      title3: 'путешественный спутник',
      title4: 'в Китае',
      subtitle:
        'Свободно исследуй Китай с精心策划ными маршрутами, местной едой и культурными историями — всё в одном приложении.',
    },
    valueProposition: {
      sectionTitle: 'Почему выбрать EasyGo China?',
      sectionDescription:
        'Открой для себя небывалый опыт в Китае с помощью наших всесторонних услуг для сопровождения во время путешествия.',
      features: [
        {
          title: 'Нет необходимости знать китайский',
          description:
            'Встроенная навигационная система оснащена многоязычными комментариями к местным достопримечательностям. Без знания китайского легко ориентироваться в достопримечательностях, еде и транспорте.',
        },
        {
          title: 'Истинная местная еда, разумные расходы',
          description:
            'Гид по еде высокого качества предлагает подлинные местные рекомендации. Избегая ловушек для туристов, наслаждайся тремя настоящими трапезами за цену одного обеда в туристической группе.',
        },
        {
          title: 'История за каждой остановкой',
          description:
            'Не только отметка на карте — но и глубокий культурный опыт. От королевских улиц до胡同-кафе, встречайте культурные сюрпризы на каждом шагу.',
        },
        {
          title: 'Один клик для покупки, немедленно готов к использованию',
          description:
            'Нет необходимости присоединяться к группам, не нужно спешить — весь путь под вашим контролем. Купите и используйте сразу, идеально подходит для спонтанных путешествий и настоящего самостоятельного путешествия.',
        },
        {
          title: 'Только 9.99 доллара в день — выгодный выбор, превосходный опыт',
          description:
            'Экономите более 70 долларов по сравнению с групповыми турами, одновременно получая больше свободы и подлинных впечатлений. Нет скрытых расходов, избегайте ловушек для туристов.',
        },
      ],
    },
    userTestimonials: {
      sectionTitle: 'Любят путешественники со всего мира',
      sectionDescription:
        'Послушайте реальные истории путешественников, которые опытывали Китай с помощью EasyGo China',
      sectionImageTitle: 'Реальные истории наших путешественников',
      sectionImageDescription: 'Узнайте, как EasyGo China изменил их опыт путешествия',
      sectionTitle2: 'Доверяют пользователи более чем из 20 стран',
      sectionDescription2:
        'Присоединяйтесь к тысячам удовлетворенных путешественников по всему миру',
      testimonials: [
        {
          text: 'EasyGo China сделал мое путешествие невероятно гладким. Функция перевода спасала меня не раз!',
          country: 'США',
        },
        {
          text: 'Местные рекомендации были просто отличными. Я нашел скрытые сокровища, которые бы никогда не нашел иначе.',
          country: 'Великобритания',
        },
        {
          text: 'Лучшее путешественное приложение, которое я когда-либо использовал. Культурные познания помогли мне действительно соединиться с местными жителями.',
          country: 'Испания',
        },
        {
          text: 'Искусственный интеллект может быстро переводить и объяснять некоторые дополнительные общие вопросы.',
          country: 'Сингапур',
        },
        {
          text: 'Встроенная навигация сделала исследование беззаботным — невероятно просто и удобно!',
          country: 'Канада',
        },
        {
          text: 'Я нашел лучшие местные рестораны, которых нет в каких-либо туристических руководствах. Это приложение действительно меняет игру!',
          country: 'Австралия',
        },
      ],
      videoTitle: 'Реальные истории наших путешественников',
      videoDescription:
        'Послушайте, как настоящие пользователи оценивают свой опыт путешествия с EasyGo China',
    },
    ourStory: {
      title: 'Наша история',
      story1:
        'Летом 2024 года я и моя жена планировали принимать нашу хорошую подругу Дженнет из Чикаго во время ее путешествия в Пекин, но были вынуждены отменить этот план. Чтобы компенсировать, мы написали для нее небольшой путеводитель.',
      story11:
        'На самом деле... сначала мы просто хотели сделать "маленький штукенчик", чтобы ей было весело — но不知不觉 мы составили подробное руководство. В нем было все: от обязательных достопримечательностей и культурных советов до транспортных указаний и рекомендаций по повседневной еде.',
      story2:
        'Мы не хотели, чтобы ей приходилось беспокоиться о еде, поэтому каждый день перечисляли 7-8 ресторанов, в каждом из которых было 5-7 рекомендуемых блюд. Мы даже написали о продуктах, способах приготовления, вкусовых особенностях и способах употребления каждого блюда — боясь, что она не будет уверена, что ей нравится. Мы также добавили десятки кафе и баров.',
      story21: 'Это лучше, чем гид — словно вы рядом со мной!',
      story22: 'Дженнет, уже после первого дня использования нашего руководства',
      story3:
        'В тот момент мы поняли: после того как за 20多年游历али более 30 стран, возможно, мы можем помочь больше друзьям, подобным Дженнет.',
      story31:
        'Тогда за следующие два месяца мы создали веб-сайт и пригласили пользователей Reddit протестировать его. Полученные отзывы поразили нас:',
      story32:
        'Это значительно точнее, чем информация, которую я нашел в интернете — мне удалось сэкономить несколько часов на поиске!',
      and: 'и',
      story33:
        'Помимо пельменей и утки по-пекински, я нашел множество скрытых кулинарных сокровищ!',
      story4:
        'Конечно, люди также упомянули некоторые проблемы — медленная загрузка, беспорядок на картах и недостаточная поддержка английского. Вот почему мы разработали приложение EasyGo China.',
      story41:
        'Это как иметь в кармане знательного местного друга — при этом цена меньше, чем за один коктейль.',
      featureMap: {
        feature1Title: 'Дружелюбно к многоязычности',
        feature1Description:
          'Система встроенной языковой поддержки, созданная для путешественников по всему миру',
        feature2Title: 'Более 30 самостоятельных путешествий',
        feature2Description: 'Охватывает 11 городов Китая',
        feature3Title: 'Более 20,000 слов',
        feature3Description: 'Объем контента за день (иногда достигает 40,000 слов!)',
      },
      statement: {
        title: 'Вот что мы хотим, чтобы каждый путешественник в Китае мог насладиться:',
        title1: 'Не умеешь говорить по-китайски? Нет проблем.',
        description1:
          'Легко найдите блюда, которые вам понравятся — даже если у них странные названия, такие как "муравьи идут по дереву" или "парные лёгкие".',
        title2: 'Нет необходимости присоединяться к группе.',
        description2:
          'Откройте то, что любят местные: подушечные массажи, чистка ушей, чайные дома и скрытые бары — как и местные.',
        title3: 'Нет необходимости часами искать в Google.',
        description3:
          'Просто откройте приложение, и ощутите, как будто друг проводит вас по местам.',
      },
      message: {
        title:
          'Используя 20-летний опыт самостоятельных путешествий, мы разработали EasyGo China, чтобы помочь вам почувствовать истинное ритмо этой удивительной страны — потому что лучшие путешествия не ограничиваются только посещением достопримечательностей.',
        description: 'Они заключаются в истинном понимании души города.',
      },
    },
    use: {
      title: 'Как использовать EasyGo China',
      description: 'Несколько простых шагов, чтобы открыть сокровища Китая.',
      description2: 'От планирования до исследования — мы поддерживаем вас на всех этапах.',
    },
    contact: {
      title: 'Оставайтесь на связи',
      description:
        'Следите за нами, чтобы получать путешественские советы, последние новости и общаться с другими путешественниками',
      socialLinks: [
        {
          description: 'Пишите нам в WeChat',
        },
        {
          description: 'Следите за нами в TikTok',
        },
        {
          description: 'Читайте наши путешественные истории',
        },
        {
          description: 'Смотрите путеводители',
        },
        {
          description: 'Присоединяйтесь к нашему сообществу',
        },
        {
          description: 'Следите за нами в X',
        },
      ],
      code: {
        open: 'Нажмите, чтобы увидеть QR-код',
        scan: 'Сканируйте QR-код для подключения',
      },
    },
    freeTrial: {
      title: 'Путешествуйте как местные',
      free: 'Бесплатно',
      description: 'Самостоятельное путешествие для новых пользователей',
      price: 'Стоимость 15.99 долларов',
      content:
        'Ваш промо-код будет отправлен сразу после регистрации на указанный при регистрации адрес электронной почты.',
      section: 'Вы получите:',
      title1: 'Без языковых барьеров',
      description1: 'Многоязычная система поможет вам легко исследовать Китай.',
      title2: 'Открытие местной еды',
      description2: 'Более 30 настоящих ресторанов с руководством по заказу.',
      title3: 'Культурные познания',
      description3: 'Более 20,000 слов культурных историй и советов.',
      title4: 'Гибкий маршрут',
      description4:
        'Начните сразу. Выберите понравившиеся достопримечательности из самостоятельных путешествий и исследуйте свободно.',
      download: 'Скачайте сейчас и начните исследовать',
      downloadText: 'Доступно на всех платформах',
      card: 'Без кредитной карты',
      cardText: 'Используйте сразу',
      info: 'Предложение действует только для новых пользователей. Применяются условия и положения.',
      registerSuccess:
        'Вы успешно зарегистрировались! Пожалуйста, скачайте приложение и войдите в систему, чтобы начать пользоваться. Желаем вам незабываемого путешествия по Китаю с EasyGo China.',
    },
    finalDownload: {
      title: 'Готова к исследованию Китая?',
      description: 'Скачайте EasyGo China и начните свое приключение уже сегодня',
      city: 'Доступные города',
      daily: 'Ежедневные маршруты',
      country: 'Страны, которые доверяют нам',
      info: 'Присоединяйтесь к тысячам путешественников, исследующих настоящий Китай',
    },
  },
  footer: {
    subscribe: {
      info: 'Это приложение переопределяет путешествия в Китай.',
      title: 'Подпишитесь на нас',
      placeholder: 'Введите ваш email',
      button: 'Подписаться',
      error: 'Что-то пошло не так',
      errorInfo: 'Попробуйте позже.',
      success: 'Успех!',
      successInfo: 'Подписка оформлена! Спасибо за подписку!',
    },
  },
} as const

export default en
