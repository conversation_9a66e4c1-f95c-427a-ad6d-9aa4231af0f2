const ko = {
  authentication: {
    emailHasNotBeenRegistered: '이메일이 등록되지 않았습니다',
    invalidOrExpired: '{{token}}이(가) 유효하지 않거나 만료되었습니다',
    otpCode: 'OTP 코드',
    passwordsNotMatch: '비밀번호가 일치하지 않습니다',
    stillValid: '{{token}}은 여전히 유효합니다',
  },
  collection: {
    cart: '카트',
    itinerary: '여행',
    product: '제품',
    redemptionCode: '환불 코드',
    user: '사용자',
    invitationCode: '초대 코드',
    voucher: '쿠폰',
  },
  error: {
    alreadyInCart: '이미 카트에 있습니다',
    alreadyPurchasedOrRedeemed: '이미 구매하거나 구입',
    canNotReuse: '재사용할 수 없습니다',
    notFound: '{{resource}} 찾을 수 없습니다',
    usageLimitReached: '{{resource}} 사용 한계 도달',
  },
  general: {
    accountCancelled: '계정 취소',
    addToCartSuccessfully: '카트에 성공적으로 추가',
    payNow: '지금 지불',
    redeemSuccessfully: '성공적으로 구매',
    defaultVoucherName: '유니버설 투어 쿠폰',
  },
  landing: {
    header: {
      features: '기능',
      reviews: '리뷰',
      story: '우리들의 이야기',
      contact: '연락',
      download: '앱 다운로드',
      origin: '원역',
    },
    hero: {
      title: '너의 끝',
      title2: '셀프 서비스',
      title3: '여행메이트',
      title4: '중국에서',
      subtitle:
        '정확하게 기획된 노선, 현지 음식과 문화 이야기로 자유롭게 중국을 탐색한다. 이 모든 것은 하나의 응용 프로그램에서 이루어진다.',
    },
    valueProposition: {
      sectionTitle: 'EasyGo China를 선택하는 이유는 무엇입니까?',
      sectionDescription:
        '우리의 전방위적인 여행 동반 서비스를 통해 전대미문의 중국 체험 여행을 시작합니다.',
      features: [
        {
          title: '중국어 필요 없음',
          description:
            '내장 내비게이션 시스템은 현지 관광지의 다국어 해설을 갖추고 있다.중국어를 이해하지 않고도 관광지, 음식 및 교통을 쉽게 탐색할 수 있다.',
        },
        {
          title: '진짜 음식, 현명한 소비',
          description:
            '고가치 음식 가이드는 제대로 된 지역 추천을 제공합니다.바가지를 씌우는 함정을 피해 여행 단식 한 끼 가격으로 정통 미식 세 끼를 즐길 수 있다.',
        },
        {
          title: '매역 뒷이야기',
          description:
            '카드만 찍는 것이 아니라 더 깊은 문화 체험을 할 수 있습니다.로열 스트리트에서 골목 카페까지 구석구석 문화 서프라이즈를 만난다.',
        },
        {
          title: '원클릭 구매, 바로 구매',
          description:
            '뭉칠 필요가 없고, 서두를 필요가 없다-전 과정을 자주적으로 여정을 통제한다.바로 사고 바로 사용할 수 있어 가자고 하면 가는 여행과 진정한 독립적인 여행에 적합하다.',
        },
        {
          title: '하루 9.99달러 - 경제적인 가격, 탁월한 경험',
          description:
            '단체 여행에 비해 70여 달러를 절약할 수 있으며, 동시에 더 많은 자유와 땅굴 체험을 얻을 수 있다.숨김 없는 소비, 바가지 함정 피하기.',
        },
      ],
    },
    userTestimonials: {
      sectionTitle: '세계 여행자들의 사랑을 받다',
      sectionDescription: 'EasyGo China를 통해 중국을 경험하는 여행자들의 실화를 듣다',
      sectionImageTitle: '우리 여행자의 실화',
      sectionImageDescription: 'EasyGo China가 어떻게 그들의 여행 경험을 변화시켰는지 발견',
      sectionTitle2: '20개국 이상의 사용자로부터 신뢰 받기',
      sectionDescription2: '세계 수천 명의 만족스러운 여행자 대열에 합류',
      testimonials: [
        {
          text: 'EasyGo China는 나의 여행을 매우 원활하게 만들었다.번역 기능이 여러 번 도움이 되지 않았습니다!',
          country: '미국',
        },
        {
          text: '현지의 추천은 매우 적절하다.이렇게 아니면 영원히 찾을 수 없는 숨겨진 보물들을 발견했어요.',
          country: '영국',
        },
        {
          text: '내가 사용한 최고의 여행 앱.문화적 통찰력은 내가 진정으로 현지인과 관계를 맺는 것을 도왔다.',
          country: '스페인',
        },
        {
          text: '인공지능 조수는 추가 상식 문제를 신속하게 번역하고 해독할 수 있다.',
          country: '싱가포르',
        },
        {
          text: '내장 내비게이션은 탐색을 아무런 고민도 없이 만든다-매우 간단하고 편리하다!',
          country: '캐나다',
        },
        {
          text: '나는 어떤 여행 안내서에서도 찾을 수 없는 최고의 현지 식당을 찾았다.이 앱은 정말 큰 변화를 가져올 수 있다!',
          country: '호주',
        },
      ],
      videoTitle: '우리 여행자의 실화',
      videoDescription:
        '실제 사용자들이 EasyGo China를 사용한 여행 경험을 어떻게 평가하는지 들어보세요',
    },
    ourStory: {
      title: '우리들의 이야기',
      story1:
        '2024년 여름, 나와 아내는 원래 우리의 좋은 친구인 시카고에서 온 제니트가 북경으로 려행하는 기간에 그녀를 접대할 계획이였지만 부득불 이 계획을 취소해야 했다.보완하기 위해서 우리는 그녀에게 작은 여행 안내서를 썼다.',
      story11:
        '사실은......처음에 우리는 그냥"작은 물건"을 만들어 그녀를 즐겁게 해 주고 싶었지만, 어느덧 우리는 상세한 내용의 수첩을 정리했다.그 안에는 필수 관광지, 문화 팁, 교통 안내서와 일상 음식 추천에 이르기까지 다양한 내용이 포함되어 있다.',
      story2:
        '우리는 그녀가 밥을 먹는 것에 대해 걱정하게 하고 싶지 않아서 매일 7~8개의 식당을 열거하고 집집마다 5~7개의 추천 메뉴를 가지고 있다.우리는 심지어 그녀가 자신이 무엇을 좋아하는지 확신하지 못할까 봐 모든 요리의 재료, 조리법, 입맛 특징 및 먹는 방법까지 썼다.우리는 또 수십 개의 카페와 술집에 추가로 가입했다.',
      story21: '이것은 가이드보다 낫다. 너희들이 내 곁에 있는 것처럼!”',
      story22: '재닛, 우리 가이드 하루만 쓴 후에',
      story3:
        '그 순간 우리는 20여 년 동안 30여 개국을 여행한 후에 제니트와 같은 친구들을 더 많이 도울 수 있을지도 모른다는 것을 깨닫게 되었다.',
      story31:
        '그래서 앞으로 두 달 동안 우리는 웹 사이트를 구축하고 Reddit 사용자를 초청하여 시험해 보았다.받은 피드백은 우리를 매우 놀라게 했다:',
      story32:
        '이것은 내가 인터넷에서 찾은 정보보다 훨씬 정확하다-내가 몇 시간 동안 열람 시간을 절약하는 데 도움을 주었다!”',
      and: '및',
      story33: '만두와 오리구이 외에도 숨겨진 음식 보물을 많이 발견했어요!”',
      story4:
        '물론 사람들은 로드가 느리고 지도가 혼란스러우며 영어 지원이 부족하다는 문제도 언급했다.이것이 바로 우리가 EasyGo China라는 앱을 개발한 이유입니다.',
      story41:
        '이것은 마치 주머니에 지식이 해박한 현지 친구를 넣은 것과 같다. 게다가 가격은 칵테일 한 잔의 돈도 안 된다.',
      featureMap: {
        feature1Title: '다국어 친화적',
        feature1Description: '글로벌 여행자를 위한 내장형 언어 지원 시스템',
        feature2Title: '30+셀프투어',
        feature2Description: '중국 11개 도시 포함',
        feature3Title: '20,000+ 단어',
        feature3Description: '하루 내용량 (일부는 40000자!)',
      },
      statement: {
        title: '다음은 우리가 중국에 있는 모든 여행자들이 즐길 수 있기를 바라는 것이다.',
        title1: '중국어 못해?괜찮습니다.',
        description1:
          '개미가 나무에 오르다 또는 부부 폐편 과 같은 이상한 이름을 가지고 있더라도 좋아하는 요리를 쉽게 찾을 수 있다.',
        title2: '뭉칠 필요가 없습니다.',
        description2:
          '발 마사지, 귀 따기, 찻집, 숨겨진 술집 등 현지인들이 좋아하는 것을 발견한다. 현지인들처럼.',
        title3: '구글 검색에 몇 시간 걸리지 않습니다.',
        description3: '이 앱을 켜기만 하면 어떤 친구가 너를 데리고 여기저기 돌아다니는 것 같다.',
      },
      message: {
        title:
          '20년의 셀프 여행 경험을 바탕으로 우리는 EasyGo China를 개발했는데, 이 신기한 나라의 진정한 맥동을 느낄 수 있도록 돕기 위한 것이다. 왜냐하면 최고의 여행은 단지 카드 관광지가 아니기 때문이다.',
        description: '그것들은 한 도시의 영혼을 진정으로 이해하는 것과 관련이 있다.',
      },
    },
    use: {
      title: 'EasyGo China 사용 방법',
      description: '단순 몇 단계, 중국의 정수를 열어라.',
      description2: '계획에서 탐색에 이르기까지 우리는 당신에게 전방위적인 지원을 제공할 것입니다.',
    },
    contact: {
      title: '연락 유지',
      description: '우리를 주목하고, 여행 팁, 최신 정보를 얻고, 다른 여행자들과 연락을 취하라',
      socialLinks: [
        {
          description: '위챗에서 우리와 채팅',
        },
        {
          description: 'TikTok에서 우리를 주목하라',
        },
        {
          description: '우리의 여행 이야기를 주목하라',
        },
        {
          description: '여행 보기 가이드',
        },
        {
          description: '우리 커뮤니티에 가입',
        },
        {
          description: 'X에서 우리를 주목하라',
        },
      ],
      code: {
        open: 'QR코드 보기 클릭',
        scan: '연결을 위한 QR코드 스캔',
      },
    },
    freeTrial: {
      title: '현지인처럼 여행하기',
      free: '공짜',
      description: '새 사용자 셀프 투어',
      price: '가치 15.99달러',
      content: '당신의 교환 코드는 등록 후 바로 등록된 메일 주소로 발송됩니다.',
      section: '당신은 얻을 것:',
      title1: '언어 장벽 없음',
      description1: '다국어 시스템으로 중국을 쉽게 탐색할 수 있습니다.',
      title2: '로컬 푸드 디스커버리',
      description2: '30여 개의 정통 식당으로 주문 안내서가 첨부되어 있다.',
      title3: '문화 통찰',
      description3: '2만여 자의 문화 이야기와 팁.',
      title4: '유연한 스케줄',
      description4: '즉시 열기.자유여행에서 마음에 드는 관광지를 골라 자유롭게 탐색하다.',
      download: '즉시 다운로드, 탐색 시작',
      downloadText: '전체 플랫폼에서 사용 가능',
      카드: '신용카드 필요 없음',
      cardText: '즉시 사용',
      info: '혜택은 신규 사용자에게만 제공됩니다.적용 약관.',
      registerSuccess:
        '성공적으로 등록되었습니다! 앱을 다운로드하고 로그인하여 사용을 시작하세요. EasyGo China와 함께 중국에서 잊지 못할 여행이 되시길 바랍니다.',
    },
    finalDownload: {
      title: '중국을 탐험할 준비가 되었습니까?',
      description: 'EasyGo China 다운로드, 오늘부터 당신의 모험을 시작하세요',
      city: '가용 가능한 도시',
      daily: '매일 스케줄',
      country: '우리 나라를 믿어요',
      info: '수천 명의 여행자와 함께 진정한 중국을 탐험하자',
    },
  },
  footer: {
    subscribe: {
      info: '이것은 중국 여행의 응용을 재정의하고 있습니다.',
      title: '우리를 구독하라',
      placeholder: '사서함을 입력하십시오',
      button: '구독',
      error: '문제가 좀 생겼어요',
      errorInfo: '이따가 다시 시도하십시오.',
      success: '성공!',
      successInfo: '구독 성공!구독해 주셔서 감사합니다!',
    },
  },
} as const

export default ko
