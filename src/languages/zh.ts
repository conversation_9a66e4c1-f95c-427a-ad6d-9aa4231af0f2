const zh = {
  authentication: {
    emailHasNotBeenRegistered: '邮箱尚未注册',
    invalidOrExpired: '{{token}}无效或已过期',
    otpCode: 'OTP验证码',
    passwordIncorrect: '密码不正确',
    passwordsNotMatch: '密码和确认密码不匹配',
    stillValid: '{{token}}还在有效期内',
  },
  collection: {
    cart: '购物车',
    itinerary: '行程',
    product: '产品',
    redemptionCode: '兑换码',
    user: '用户',
    invitationCode: '邀请码',
    voucher: '优惠券',
  },
  error: {
    alreadyInCart: '已在购物车中',
    alreadyPurchasedOrRedeemed: '已购买(或兑换)',
    canNotReuse: '不能重复使用',
    notFound: '{{resource}}未找到',
    usingLimitReached: '{{resource}}使用次数已达上限',
  },
  general: {
    accountCancelled: '账号已注销',
    addToCartSuccessfully: '成功加入购物车',
    payNow: '确认支付',
    redeemSuccessfully: '成功兑换',
    defaultVoucherName: '通用优惠券',
  },
  landing: {
    header: {
      features: '功能',
      reviews: '评论',
      story: '我们的故事',
      contact: '联系',
      download: '下载应用',
      origin: '原站',
    },
    hero: {
      title: '你的终极',
      title2: '自助的',
      title3: '旅行伴侣',
      title4: '在中国',
      subtitle: '凭借精心策划的路线、当地美食和文化故事，自由探索中国——这一切尽在一款应用中。',
    },
    valueProposition: {
      sectionTitle: '为什么选择 EasyGo China?',
      sectionDescription: '借助我们全方位的旅行陪伴服务，开启前所未有的中国体验之旅。',
      features: [
        {
          title: '无需中文',
          description:
            '内置导航系统配有当地景点的多语言解说。无需懂中文，即可轻松导航景点、美食及交通。',
        },
        {
          title: '地道美食，明智消费',
          description:
            '高价值美食指南提供地道的本地推荐。避开宰客陷阱，以一顿旅游团餐的价格畅享三顿正宗美餐。',
        },
        {
          title: '每一站背后的故事',
          description:
            '不止于打卡 —— 更有深度文化体验。从皇家街道到胡同咖啡馆，在每个角落邂逅文化惊喜。',
        },
        {
          title: '一键购买，即买即用',
          description:
            '无需跟团，不用匆忙 —— 全程自主掌控旅程。即买即用，完美适配说走就走的旅行与真正的独立出行。',
        },
        {
          title: '每日仅需 9.99 美元 —— 实惠之选，卓越体验',
          description:
            '与跟团游相比节省 70 多美元，同时收获更多自由与地道体验。无隐藏消费，避开宰客陷阱。',
        },
      ],
    },
    userTestimonials: {
      sectionTitle: '受到世界各地旅行者的喜爱',
      sectionDescription: '聆听那些通过 EasyGo China 体验中国的旅行者的真实故事',
      sectionImageTitle: '我们旅行者的真实故事',
      sectionImageDescription: '发现 EasyGo China 如何改变了他们的旅行体验',
      sectionTitle2: '受到 20 多个国家用户的信任',
      sectionDescription2: '加入全球成千上万满意的旅行者行列',
      testimonials: [
        {
          text: 'EasyGo China 让我的旅行变得极其顺畅。翻译功能无数次帮了我的忙！',
          country: '美国',
        },
        {
          text: '当地的推荐非常到位。我发现了那些若非如此永远也找不到的隐藏宝藏。',
          country: '英国',
        },
        {
          text: '我用过的最好的旅行应用。文化洞察帮助我真正地与当地人建立了联系。',
          country: '西班牙',
        },
        {
          text: '一个人工智能助手能够迅速翻译并解读一些额外的常识问题。',
          country: '新加坡',
        },
        {
          text: '内置导航让探索变得毫无烦恼 —— 超级简单方便！',
          country: '加拿大',
        },
        {
          text: '我找到了最棒的当地餐馆，这些餐馆在任何旅游指南里都找不到。这款应用真的能带来巨大改变！',
          country: '澳大利亚',
        },
      ],
      videoTitle: '我们旅行者的真实故事',
      videoDescription: '聆听真实用户如何评价他们使用 EasyGo China 的旅行体验',
    },
    ourStory: {
      title: '我们的故事',
      story1:
        '2024 年夏天，我和妻子原本计划在我们的好朋友、来自芝加哥的珍妮特去北京旅行期间招待她，却不得不取消了这一计划。为了弥补，我们给她写了一份小小的旅行指南。',
      story11:
        '其实…… 一开始我们只是想做个 “小玩意儿” 帮她玩得开心 —— 但不知不觉间，我们竟整理出了一本内容详实的手册。里面涵盖了各种内容，从必游景点、文化小贴士，到交通指南和日常美食推荐，应有尽有。',
      story2:
        '我们不想让她为吃饭操心，于是每天列出 7 到 8 家餐厅，每家都有 5 到 7 道推荐菜品。我们甚至还写了每道菜的食材、烹饪方法、口味特点以及食用方法 —— 就怕她不确定自己喜欢什么。我们还额外加入了几十家咖啡馆和酒吧呢。',
      story21: '“这比导游还好 —— 就像你们就在我身边一样！”',
      story22: '珍妮特，在只用了我们的指南一天之后',
      story3:
        '那一刻让我们意识到：在 20 多年间游历了 30 多个国家之后，或许我们可以帮助更多像珍妮特这样的朋友。',
      story31:
        '于是接下来的两个月里，我们搭建了一个网站，并邀请 Reddit 用户进行试用。收到的反馈让我们惊喜不已：',
      story32: '“这比我在网上找到的信息准确得多 —— 帮我省了好几个小时的查阅时间！”',
      and: '和',
      story33: '“除了饺子和烤鸭之外，我还发现了好多隐藏的美食宝藏！”',
      story4:
        '当然，人们也提到了一些问题 —— 加载缓慢、地图混乱，以及英语支持不足。这就是我们开发 EasyGo China 这款应用的原因。',
      story41: '这就像在口袋里装了一位知识渊博的本地朋友 —— 而且价格还不到一杯鸡尾酒的钱。',
      featureMap: {
        feature1Title: '多语言友好',
        feature1Description: '为全球旅行者打造的内置语言支持系统',
        feature2Title: '30+自助游',
        feature2Description: '涵盖中国11个城市',
        feature3Title: '20,000+词语',
        feature3Description: '每天的内容量（有些达到40,000字！）',
      },
      statement: {
        title: '以下是我们希望每一位在中国的旅行者都能享受到的：',
        title1: '不会说中文？没问题。',
        description1:
          '轻松找到你会喜欢的菜肴——即使它们有着奇怪的名字，比如“蚂蚁上树”或“夫妻肺片”。',
        title2: '不需要跟团。',
        description2:
          '发现当地人喜爱的事物，比如足底按摩、采耳、茶馆和隐藏的酒吧——就像当地人那样。',
        title3: '不需要数小时的谷歌搜索。',
        description3: '只需打开这款应用，就会感觉像有个朋友在带你四处逛逛。',
      },
      message: {
        title:
          '凭借20年的自助旅行经验，我们开发了EasyGo China，旨在帮助你感受这个神奇国家真正的脉动——因为最棒的旅行不仅仅是打卡景点。',
        description: '它们关乎真正理解一座城市的灵魂。',
      },
    },
    use: {
      title: '如何使用 EasyGo China',
      description: '简单几步，开启中国的精华之处。',
      description2: '从规划到探索，我们都会为你提供全方位支持。',
    },
    contact: {
      title: '保持联系',
      description: '关注我们，获取旅行小贴士、最新资讯，并与其他旅行者建立联系',
      socialLinks: [
        {
          description: '在微信上和我们聊天',
        },
        {
          description: '在TikTok上关注我们',
        },
        {
          description: '关注我们的旅行故事',
        },
        {
          description: '观看旅行指南',
        },
        {
          description: '加入我们的社区',
        },
        {
          description: '在X上关注我们',
        },
      ],
      code: {
        open: '点击查看二维码',
        scan: '扫描二维码进行连接',
      },
    },
    freeTrial: {
      title: '像当地人一样旅行',
      free: '免费',
      description: '新用户自助游',
      price: '价值15.99美元',
      content: '您的兑换码将在注册后立即发送至您注册的邮箱地址。',
      section: '您将获得：',
      title1: '无语言障碍',
      description1: '多语言系统让您轻松探索中国。',
      title2: '本地美食发现',
      description2: '30 多家正宗餐厅，附有点餐指南。',
      title3: '文化洞察',
      description3: '2 万多字的文化故事与小贴士。',
      title4: '灵活行程',
      description4: '即刻开启。从自助游中挑选心仪景点，自由探索。',
      download: '立即下载，开始探索',
      downloadText: '全平台均可使用',
      card: '无需信用卡',
      cardText: '即刻使用',
      info: '优惠仅限新用户享受。适用条款与条件。',
      registerSuccess:
        '您已成功注册！请下载应用并登录以开始使用。祝您与EasyGo China一起在中国度过难忘的旅程。',
    },
    finalDownload: {
      title: '准备好探索中国了吗？',
      description: '下载 EasyGo China，从今天开始你的冒险吧',
      city: '可用城市',
      daily: '每日行程',
      country: '信任我们的国家',
      info: '加入成千上万的旅行者，一起探索真实的中国',
    },
  },
  footer: {
    subscribe: {
      info: '这款正在重新定义中国旅行的应用。',
      title: '订阅我们',
      placeholder: '请输入您的邮箱',
      button: '订阅',
      error: '出了点问题',
      errorInfo: '请稍后再试。',
      success: '成功！',
      successInfo: '订阅成功！感谢你的订阅！',
    },
  },
} as const

export default zh
