import type { NestedKeysStripped } from '@payloadcms/translations'

import en from './en'
import zh from './zh'
import ja from './ja'
import ko from './ko'
import ru from './ru'
import es from './es'
import fr from './fr'
import de from './de'
import pt from './pt'
import it from './it'
import ms from './ms'
import th from './th'

export const translations = {
  en,
  zh,
  ja,
  ko,
  ru,
  es,
  fr,
  de,
  pt,
  it,
  ms,
  th,
}

export type TranslationsObject = typeof translations.en

export type TranslationsKeys = NestedKeysStripped<TranslationsObject>
