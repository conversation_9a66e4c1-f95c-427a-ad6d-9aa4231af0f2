// storage-adapter-import-placeholder
import { mongooseAdapter } from '@payloadcms/db-mongodb' // database-adapter-import

import sharp from 'sharp' // sharp-import
import path from 'path'
import type { CollectionSlug } from 'payload'
import { buildConfig } from 'payload'
import { fileURLToPath } from 'url'

import { en } from '@payloadcms/translations/languages/en'
import { zh } from '@payloadcms/translations/languages/zh'
import { resendAdapter } from '@payloadcms/email-resend'
import { s3Storage } from '@payloadcms/storage-s3'
import { translator, openAIResolver } from '@payload-enchants/translator'
import { NodeHttpHandler } from '@aws-sdk/node-http-handler'
import https from 'https'

import { Categories } from './collections/Categories'
import { Media } from './collections/Media'
import { Pages } from './collections/Pages'
import { Posts } from './collections/Posts'
import { Users } from './collections/Users'
import { Cities } from './collections/Cities'
import { Itineraries } from './collections/Itineraries'
import { ExampleBlocks } from './collections/ExampleBlocks'
import { Restaurants } from './collections/Restaurants'
import { Locations } from './collections/Locations'
import { GuideCategories } from './collections/GuideCategories'
import { ItinerariesTags } from './collections/ItinerariesTags'
import { GuidePost } from './collections/GuidePost'
import { BusinessHours } from './collections/BusinessHours'
import { DownloadRecords } from './collections/DownloadRecords'
import { Footer } from './Footer/config'
import { Header } from './Header/config'
import { plugins } from './plugins'
import { defaultLexical } from '@/fields/defaultLexical'
import { getServerSideURL } from './utilities/getURL'
import { Audio } from './collections/Audio'
import { translations } from '@/languages/translations'
import { OtpCode } from '@/collections/OptCodes'
import { Prices } from '@/collections/Prices'
import { Products } from '@/collections/Products'
import { Orders } from '@/collections/Orders'
import { Carts } from '@/collections/Carts'
import { RedemptionCodes } from '@/collections/RedemptionCodes'
import { UserOrderItineraries } from '@/collections/UserOrderItineraries'
import { CityPost } from '@/collections/CityPost'
import { InvitationCodes } from './collections/InvitationCodes'
import { Voucher } from './collections/Voucher'
import { Comment } from './collections/Comment'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

const agent = new https.Agent({
  maxSockets: 100,
})

const collections = [
  Cities,
  Itineraries,
  Locations,
  BusinessHours,
  ItinerariesTags,
  Media,
  Audio,
  GuidePost,
  GuideCategories,
  Posts,
  Categories,
  Users,
  Pages,
  DownloadRecords,
  ExampleBlocks,
  OtpCode,
  Prices,
  Products,
  Orders,
  Carts,
  RedemptionCodes,
  UserOrderItineraries,
  CityPost,
  InvitationCodes,
  Voucher,
  Comment,
]

export default buildConfig({
  admin: {
    components: {
      // The `BeforeLogin` component renders a message that you see while logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below and the import `BeforeLogin` statement on line 15.
      beforeLogin: ['@/components/BeforeLogin'],
      // The `BeforeDashboard` component renders the 'welcome' block that you see after logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below and the import `BeforeDashboard` statement on line 15.
      // beforeDashboard: ['@/components/BeforeDashboard'],
      graphics: {
        Logo: './components/Logo/Logo',
        Icon: './components/Logo/Logo',
      },
    },
    importMap: {
      baseDir: path.resolve(dirname),
    },
    user: Users.slug,
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667,
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024,
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900,
        },
      ],
    },
    meta: {
      title: 'Administration panel',
      titleSuffix: ' | EasyGo China',
      description: 'The best tour guide in China',
      icons: [
        {
          rel: 'icon',
          type: 'image/png',
          url: '/favicon.ico',
        },
      ],
    },
  },
  // This config helps us configure global or default features that the other editors can inherit
  editor: defaultLexical,
  // database-adapter-config-start
  db: mongooseAdapter({
    connectOptions: {
      useFacet: false,
    },
    url: process.env.DATABASE_URI,
  }),
  // database-adapter-config-end
  collections,
  cors: [getServerSideURL()].filter(Boolean),
  globals: [Header, Footer],
  plugins: [
    ...plugins,
    // storage-adapter-placeholder
    s3Storage({
      collections: {
        media: {
          prefix: 'media',
        },
        audio: {
          prefix: 'audios',
        },
      },
      bucket: process.env.S3_BUCKET_NAME!,
      config: {
        credentials: {
          accessKeyId: process.env.S3_ACCESS_KEY_ID!,
          secretAccessKey: process.env.S3_SECRET_ACCESS!,
        },
        region: process.env.S3_REGION,
        endpoint: process.env.S3_ENDPOINT,
        requestHandler: new NodeHttpHandler({
          connectionTimeout: 5000, // 5 seconds
          socketTimeout: 5000, // 5 seconds
          httpsAgent: agent,
        }),
      },
    }),
    translator({
      collections: collections.map((collection) => collection.slug as CollectionSlug),
      globals: [],
      resolvers: [
        openAIResolver({
          apiKey: process.env.OPENAI_API_KEY!,
          baseUrl: process.env.OPENAI_BASE_URL!,
          model: process.env.OPENAI_MODEL,
          prompt: ({ localeFrom, localeTo, texts }) =>
            `
              Translate the following array: ${JSON.stringify(texts)} from ${localeFrom} to ${localeTo}.
              Return the translated array with the exact same structure, preserving empty strings and spaces.
              Provide only the translated array—no extra text, explanations, or markdown.
            `,
        }),
      ],
    }),
  ],
  secret: process.env.PAYLOAD_SECRET,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  i18n: {
    fallbackLanguage: 'en',
    supportedLanguages: { en, zh },
    translations,
  },
  email: resendAdapter({
    defaultFromAddress: '<EMAIL>',
    defaultFromName: 'EasyGo China',
    apiKey: process.env.RESEND_API_KEY || '',
  }),
  localization: {
    locales: [
      {
        label: 'English',
        code: 'en',
      },
      {
        label: '한국어',
        code: 'ko',
      },
      {
        label: '日本語',
        code: 'ja',
      },
      {
        label: 'Deutsch',
        code: 'de',
      },
      {
        label: 'Español',
        code: 'es',
      },
      {
        label: 'Français',
        code: 'fr',
      },
      {
        label: 'Русский язык',
        code: 'ru',
      },
      {
        label: 'Bahasa Melayu',
        code: 'ms',
      },
      {
        label: 'Português',
        code: 'pt',
      },
      {
        label: 'Italiano',
        code: 'it',
      },
      {
        label: 'ภาษาไทย',
        code: 'th',
      },
      {
        label: '中文',
        code: 'zh',
      },
    ],
    defaultLocale: 'en',
  },
})
