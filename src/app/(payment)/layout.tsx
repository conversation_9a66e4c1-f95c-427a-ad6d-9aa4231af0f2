import { Geist<PERSON><PERSON> } from 'geist/font/mono'
import { GeistSans } from 'geist/font/sans'
import { cn } from 'src/utilities/cn'

import { Providers } from '@/providers'
import { InitTheme } from '@/providers/Theme/InitTheme'

import './globals.css'

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html className={cn(GeistSans.variable, GeistMono.variable)} lang="en" suppressHydrationWarning>
      <head>
        <InitTheme />
        <link href="/favicon.ico" rel="icon" sizes="32x32" />
      </head>
      <body className="justify-center">
        <Providers>{children}</Providers>
      </body>
    </html>
  )
}
