'use client'

// import { useTranslation } from '@payloadcms/ui'
import { Elements, PaymentElement, useElements, useStripe } from '@stripe/react-stripe-js'
import type { StripeElementLocale } from '@stripe/stripe-js'
import { loadStripe } from '@stripe/stripe-js'
import Image from 'next/image'
import { useSearchParams } from 'next/navigation'
import type { FormEvent } from 'react'
import { Fragment, Suspense, useEffect, useMemo, useState } from 'react'

// import type { TranslationsKeys, TranslationsObject } from '@/languages/translations'
import { User, Voucher } from '@/payload-types'
import { formatCurrencyNumber, formatDecimal } from '@/utilities/formatNumber'
import { getClientSideURL } from '@/utilities/getURL'

const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || ''
const stripePromise = loadStripe(stripePublishableKey)

type PaymentItem = {
  heroImage: { url: string }
  id: string
  productId: { defaultPriceCurrency: string; defaultPriceUnitAmount: number }
  title: string
}

type PaymentItemListProps = {
  discount: number
  itineraries: PaymentItem[]
  locale: string
}

function fetchRequest<T>(url: string, token: string): Promise<T> {
  return new Promise((resolve, reject) => {
    fetch(url, {
      credentials: 'include',
      headers: { Authorization: `Bearer ${token}` },
    })
      .then((res) => res.json())
      .then((data) => resolve(data))
      .catch(reject)
  })
}

function useItineraries(itineraryIdStr: string | null, locale: string, token: string | null) {
  const [itineraries, setItineraries] = useState<PaymentItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!itineraryIdStr) {
      return setError('Please provide an itinerary id!')
    }
    if (!token) {
      return setError('Please provide a token!')
    }

    setLoading(true)
    fetchRequest<{ docs: PaymentItem[] }>(
      `/api/itineraries?${itineraryIdStr
        .split(',')
        .map((id) => `where[id][in][]=${id}`)
        .join(
          '&',
        )}&select[title]=true&select[productId]=true&select[heroImage]=true&depth=1&locale=${locale}`,
      token,
    )
      .then((data) => {
        setItineraries(data.docs)
        setError(null)
      })
      .catch((err) => setError(err.message || 'Failed to load itineraries'))
      .finally(() => setLoading(false))
  }, [itineraryIdStr, token])

  return { itineraries, loading, error }
}

function useUser(token: string | null) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!token) {
      return setError('Please provide a token!')
    }

    setLoading(true)
    fetchRequest<{ user: User | null }>('/api/users/me', token)
      .then((data) => {
        setUser(data.user)
        setError(null)
      })
      .catch((err) => setError(err.message || 'Failed to load user'))
      .finally(() => setLoading(false))
  }, [token])

  return { user, loading, error }
}

function Loading() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto" />
        <p>Loading...</p>
      </div>
    </div>
  )
}

function PaymentItemList(props: PaymentItemListProps) {
  const { discount, itineraries, locale } = props

  const totalAmount = useMemo(
    () =>
      itineraries.reduce((acc, itinerary) => acc + itinerary.productId.defaultPriceUnitAmount, 0),
    [itineraries],
  )

  if (itineraries.length === 0) {
    return <div className="error-message">No itineraries found!</div>
  }

  return (
    <div className="px-6 sm:container">
      {itineraries.map((itinerary) => (
        <section
          key={itinerary.id}
          className="bg-stone-200 first:rounded-t-xl flex flex-row p-3 text-black"
        >
          <Image
            alt={itinerary.id}
            className="flex-none h-16 object-cover rounded-xl w-16"
            src={`${getClientSideURL()}${itinerary.heroImage.url}`}
            width={64}
            height={64}
          />
          <p className="flex-auto p-2 text-xs multi-truncate">{itinerary.title}</p>
          <div className="flex flex-col text-sm">
            <span>
              {formatCurrencyNumber(
                formatDecimal(itinerary.productId.defaultPriceUnitAmount * discount, 'ceil'),
                itinerary.productId.defaultPriceCurrency,
                locale,
              )}
            </span>
            {discount < 1 ? (
              <span className="line-through text-gray-500 text-sm">
                {formatCurrencyNumber(
                  itinerary.productId.defaultPriceUnitAmount,
                  itinerary.productId.defaultPriceCurrency,
                  locale,
                )}
              </span>
            ) : null}
          </div>
        </section>
      ))}
      {discount < 1 ? (
        <p className="bg-stone-200 flex flex-row font-bold justify-between p-3 text-black text-sm">
          <span>Discount</span>
          <span>
            -
            {formatCurrencyNumber(
              formatDecimal(totalAmount * (1 - discount), 'ceil'),
              itineraries[0].productId.defaultPriceCurrency,
              locale,
            )}
          </span>
        </p>
      ) : null}
      <p className="bg-stone-200 flex flex-row font-bold justify-between last:rounded-b-xl p-3 text-black text-sm">
        <span>Total</span>
        <span>
          {formatCurrencyNumber(
            formatDecimal(totalAmount * discount, 'ceil'),
            itineraries[0].productId.defaultPriceCurrency,
            locale,
          )}
        </span>
      </p>
    </div>
  )
}

function PaymentForm() {
  // const { t } = useTranslation<TranslationsObject, TranslationsKeys>()

  const stripe = useStripe()
  const elements = useElements()

  const [message, setMessage] = useState<string>()
  const [isLoading, setIsLoading] = useState(false)
  const [shown, setShown] = useState(false)

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()

    if (!stripe || !elements) {
      // Stripe.js hasn't yet loaded.
      // Make sure to disable form submission until Stripe.js has loaded.
      return
    }

    setIsLoading(true)

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        // Make sure to change this to your payment completion page
        return_url: `${getClientSideURL()}/checkout/success`,
      },
    })

    // This point will only be reached if there is an immediate error when
    // confirming the payment. Otherwise, your customer will be redirected to
    // your `return_url`. For some payment methods like iDEAL, your customer will
    // be redirected to an intermediate site first to authorize the payment, then
    // redirected to the `return_url`.

    setMessage(error.message)
    setIsLoading(false)
  }

  const confirmBtnTxt = useMemo(() => (isLoading ? 'Loading' : 'Pay Now'), [isLoading])

  const paymentElementOptions = {
    layout: 'auto',
  } as const

  const onPaymentElementReady = () => {
    setShown(true)
  }

  return (
    <form className="px-6 sm:container" id="payment-form" onSubmit={handleSubmit}>
      {shown && <h2 className="font-bold mb-4 mt-8 text-lg">Payment Method</h2>}
      <PaymentElement
        id="payment-element"
        options={paymentElementOptions}
        onReady={onPaymentElementReady}
      />
      {shown && (
        <button className="bg-primary my-3 p-3 rounded w-full" id="submit" disabled={isLoading}>
          <span className="text-white" id="button-text">
            {confirmBtnTxt}
          </span>
        </button>
      )}
      {message && (
        <div className="error-message" id="payment-message">
          {message}
        </div>
      )}
    </form>
  )
}

function PaymentExtra() {
  return (
    <div className="mt-8 px-6 sm:container text-center">
      <a className="text-gray-300 text-sm" href="https://stripe.com">
        Powered by <b>stripe</b>
      </a>
    </div>
  )
}

function CheckoutForm() {
  const searchParams = useSearchParams()

  const clientSecret = searchParams.get('client_secret')
  const itineraryIdStr = searchParams.get('itineraries')
  const locale = (searchParams.get('locale') || window.navigator.language) as StripeElementLocale
  const token = searchParams.get('token')
  const voucherId = searchParams.get('voucherId')

  const {
    itineraries,
    loading: itLoading,
    error: itError,
  } = useItineraries(itineraryIdStr, locale, token)
  const { user, loading: userLoading, error: userError } = useUser(token)

  const [discount, setDiscount] = useState(1)

  useEffect(() => {
    if (!token || !voucherId) return
    fetchRequest<Voucher | null>(`/api/vouchers/${voucherId}`, token)
      .then((voucher) => setDiscount(voucher?.discount ?? 1))
      .catch((reason) => console.error(reason))
  }, [token, voucherId])

  if (!clientSecret) {
    return <div className="error-message">Please provide a valid client secret!</div>
  }

  if (itLoading || userLoading) {
    return <Loading />
  }
  if (userError) {
    return <div className="error-message">{userError}</div>
  }
  if (!user) {
    return <div className="error-message">Please provide a valid token!</div>
  }
  if (itError) {
    return <div className="error-message">{itError}</div>
  }

  const appearance = {
    theme: 'stripe',
    labels: 'floating',
  } as const

  return (
    <Fragment>
      <PaymentItemList discount={discount} itineraries={itineraries} locale={locale} />
      <Elements stripe={stripePromise} options={{ appearance, clientSecret, locale }}>
        <PaymentForm />
      </Elements>
      <PaymentExtra />
    </Fragment>
  )
}

export default function CheckoutPage() {
  return (
    <Suspense>
      <CheckoutForm />
    </Suspense>
  )
}
