import { getNextRequestI18n } from '@payloadcms/next/utilities'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import type { TranslationsKeys, TranslationsObject } from '@/languages/translations'
import { RedemptionCode } from '@/payload-types'
import { getMeUser } from '@/utilities/getMeUser'
import buildConfig from '@payload-config'

export async function POST(req: NextRequest) {
  try {
    const config = await buildConfig
    const i18n = await getNextRequestI18n<TranslationsObject, TranslationsKeys>({ config })

    const body = await req.json()
    const { itineraryId, numbers } = body
    if (!itineraryId) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'itineraryId' }) },
        { status: 400 },
      )
    }
    if (!numbers) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'numbers' }) },
        { status: 400 },
      )
    }

    const { user } = await getMeUser()
    if (!user) {
      return NextResponse.json({ error: i18n.t('error:unauthorized') }, { status: 401 })
    }

    const payload = await getPayload({ config })

    const itinerary = await payload.findByID({
      collection: 'itineraries',
      id: itineraryId,
      depth: 1,
      disableErrors: true,
    })
    if (!itinerary) {
      return NextResponse.json(
        { error: i18n.t('error:notFound', { resource: i18n.t('collection:itinerary') }) },
        { status: 404 },
      )
    }

    const redemptionCode = await payload.db.findOne<RedemptionCode>({
      collection: 'redemption-codes',
      where: {
        userId: { equals: user.id },
        numbers: { equals: numbers },
      },
    })
    if (!redemptionCode) {
      return NextResponse.json(
        { error: i18n.t('error:notFound', { resource: i18n.t('collection:redemptionCode') }) },
        { status: 404 },
      )
    }
    if (redemptionCode.redeemed) {
      return NextResponse.json({ error: i18n.t('error:canNotReuse') }, { status: 400 })
    }

    const orderCount = await payload.db.count({
      collection: 'user-order-itineraries',
      where: { userId: { equals: user.id }, itineraryId: { equals: itineraryId } },
    })
    if (orderCount.totalDocs > 0) {
      return NextResponse.json(
        { error: i18n.t('error:alreadyPurchasedOrRedeemed') },
        { status: 400 },
      )
    }

    let product = itinerary.productId
    if (typeof product === 'string') {
      const productDoc = await payload.findByID({
        collection: 'products',
        id: product,
      })
      if (!productDoc) {
        return NextResponse.json(
          { error: i18n.t('error:notFound', { resource: i18n.t('collection:product') }) },
          { status: 404 },
        )
      }
      product = productDoc
    }

    const order = await payload.db.create({
      collection: 'orders',
      data: {
        userId: user.id,
        stripePaymentIntentID: numbers,
        itineraryId,
        // @ts-ignore
        amount: product.unitAmount,
        // @ts-ignore
        currency: product.defaultPriceCurrency,
      },
    })
    await payload.db.create({
      collection: 'user-order-itineraries',
      data: {
        userId: user.id,
        itineraryId,
        orderId: order.id,
      },
    })

    await payload.db.updateOne({
      collection: 'redemption-codes',
      id: redemptionCode.id,
      data: { redeemed: true },
    })

    return NextResponse.json({ message: i18n.t('general:redeemSuccessfully') }, { status: 200 })
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Sever Error' }, { status: 500 })
  }
}
