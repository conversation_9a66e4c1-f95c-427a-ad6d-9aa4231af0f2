import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/utilities/jwt'
import { generatePresignedUrl } from '@/utilities/s3'
import { getPayload } from 'payload'
import config from '@payload-config'
import { equal } from 'assert'

export async function GET(req: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const token = req.nextUrl.searchParams.get('token')

    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 })
    }

    // 验证 token
    const { email, formId, submissionId } = verifyToken(token)
    // 更新表单提交记录
    const result = await payload.update({
      collection: 'form-submissions',
      data: {
        isValidEmail: true,
      },
      where: {
        submissionData: {
          equals: {
            field: 'email',
            value: email,
            id: submissionId,
          },
        },
      },
    })

    // 创建下载记录
    await payload.create({
      collection: 'download-records',
      data: {
        email,
        formId,
        submissionId: result?.docs[0]?.id,
        downloadTime: new Date().toISOString(),
        ipAddress: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip'),
      },
    })

    const pdfName =
      formId === process.env.NEXT_PUBLIC_ICE_AND_FIRE_FORM_ID
        ? 'a-song-of-ice-and-fire.pdf'
        : 'fuzhou-houfu-parade.pdf'
    // 生成下载链接
    const downloadUrl = await generatePresignedUrl(`pdfs/${pdfName}`)

    return NextResponse.json({ downloadUrl })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'Failed to process download' },
      { status: 400 },
    )
  }
}
