import { getNextRequestI18n } from '@payloadcms/next/utilities'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import isEmail from 'validator/es/lib/isEmail'

import { CodeType } from '@/enums/code-type'
import type { TranslationsKeys, TranslationsObject } from '@/languages/translations'
import buildConfig from '@payload-config'

export async function POST(req: NextRequest) {
  try {
    const config = await buildConfig
    const i18n = await getNextRequestI18n<TranslationsObject, TranslationsKeys>({ config })

    const body = await req.json()
    const { code, email, type } = body
    if (!isEmail(email)) {
      return NextResponse.json({ error: i18n.t('validation:emailAddress') }, { status: 400 })
    }

    const isForgot = type === CodeType.FORGOT
    const isRegister = type === CodeType.REGISTER
    if ((!isForgot && !isRegister) || !code) {
      return NextResponse.json({ error: i18n.t('authentication:unableToVerify') }, { status: 400 })
    }

    const payload = await getPayload({ config })
    const codeDoc = await payload.db.findOne({
      collection: 'opt-codes',
      where: {
        email: { equals: email },
        code: { equals: code },
        type: { equals: type },
        expiresAt: {
          greater_than: new Date().toISOString(),
        },
        verified: { equals: false },
      },
    })
    if (!codeDoc) {
      return NextResponse.json(
        {
          error: i18n.t('authentication:invalidOrExpired', {
            token: i18n.t('authentication:otpCode'),
          }),
        },
        { status: 400 },
      )
    }
    await payload.db.updateOne({
      collection: 'opt-codes',
      data: { verified: true },
      id: codeDoc.id,
    })

    return NextResponse.json({ message: i18n.t('authentication:emailVerified') }, { status: 200 })
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Sever Error' }, { status: 500 })
  }
}
