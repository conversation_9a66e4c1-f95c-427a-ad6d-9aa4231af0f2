import { getNextRequestI18n } from '@payloadcms/next/utilities'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import isEmail from 'validator/es/lib/isEmail'

import { CodeType } from '@/enums/code-type'
import { EmailTemplateType } from '@/enums/email-template-type'
import type { TranslationsKeys, TranslationsObject } from '@/languages/translations'
import { generateRandomCode } from '@/utilities/generateRandomCode'
import { getEmailTemplate } from '@/utilities/getEmailTemplate'
import buildConfig from '@payload-config'

export async function POST(req: NextRequest) {
  try {
    const config = await buildConfig
    const i18n = await getNextRequestI18n<TranslationsObject, TranslationsKeys>({ config })

    const body = await req.json()
    const { email, type } = body
    if (!isEmail(email)) {
      return NextResponse.json({ error: i18n.t('validation:emailAddress') }, { status: 400 })
    }

    const isForgot = type === CodeType.FORGOT
    const isRegister = type === CodeType.REGISTER
    if (!isForgot && !isRegister) {
      return NextResponse.json({ error: i18n.t('authentication:unableToVerify') }, { status: 400 })
    }

    const payload = await getPayload({ config })
    const userCount = await payload.db.count({
      collection: 'users',
      where: { email: { equals: email } },
    })
    const { totalDocs } = userCount
    if (isForgot && totalDocs === 0) {
      return NextResponse.json(
        { error: i18n.t('authentication:emailHasNotBeenRegistered') },
        { status: 400 },
      )
    }
    if (isRegister && totalDocs > 0) {
      return NextResponse.json(
        { error: i18n.t('error:userEmailAlreadyRegistered') },
        { status: 400 },
      )
    }

    const codeCount = await payload.db.count({
      collection: 'opt-codes',
      where: {
        email: { equals: email },
        type: { equals: type },
        expiresAt: {
          greater_than: new Date().toISOString(),
        },
        verified: { equals: false },
      },
    })
    if (codeCount.totalDocs > 0) {
      return NextResponse.json(
        {
          error: i18n.t('authentication:stillValid', {
            token: i18n.t('authentication:otpCode'),
          }),
        },
        { status: 400 },
      )
    }

    const code = generateRandomCode()
    const html = getEmailTemplate(EmailTemplateType.OTPCODE, {
      language: i18n.language,
      variables: { CODE: code, TITLE: i18n.t('authentication:otpCode') },
    })
    await payload.sendEmail({
      from: `"${payload.email.defaultFromName}" <${payload.email.defaultFromAddress}>`,
      to: email,
      subject: i18n.t('authentication:otpCode'),
      html,
    })

    await payload.db.upsert({
      collection: 'opt-codes',
      data: {
        email,
        code,
        type,
        expiresAt: new Date(Date.now() + 1000 * 60 * 5).toISOString(),
        verified: false,
      },
      where: { email: { equals: email }, type: { equals: type } },
    })

    return NextResponse.json({ message: i18n.t('authentication:emailSent') }, { status: 200 })
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Sever Error' }, { status: 500 })
  }
}
