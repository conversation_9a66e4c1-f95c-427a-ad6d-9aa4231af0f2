import { getNextRequestI18n } from '@payloadcms/next/utilities'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import isEmail from 'validator/es/lib/isEmail'

import { CodeType } from '@/enums/code-type'
import type { TranslationsKeys, TranslationsObject } from '@/languages/translations'
import type { User } from '@/payload-types'
import { generatePasswordHashWithSalt } from '@/utilities/generatePasswordSaltHash'
import buildConfig from '@payload-config'

export async function POST(req: NextRequest) {
  try {
    const config = await buildConfig
    const i18n = await getNextRequestI18n<TranslationsObject, TranslationsKeys>({ config })

    const body = await req.json()
    const { email, password, confirm } = body
    if (!isEmail(email)) {
      return NextResponse.json({ error: i18n.t('validation:emailAddress') }, { status: 400 })
    }
    if (!password) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'password' }) },
        { status: 400 },
      )
    }
    if (!confirm) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'confirm' }) },
        { status: 400 },
      )
    }

    const payload = await getPayload({ config })
    const userDoc = await payload.db.findOne<User>({
      collection: 'users',
      where: { email: { equals: email } },
    })
    if (userDoc === null) {
      return NextResponse.json(
        { error: i18n.t('authentication:emailHasNotBeenRegistered') },
        { status: 400 },
      )
    }
    if (password !== confirm) {
      return NextResponse.json(
        { error: i18n.t('authentication:passwordsNotMatch') },
        { status: 400 },
      )
    }

    const codeDoc = await payload.db.findOne({
      collection: 'opt-codes',
      where: {
        email: { equals: email },
        type: { equals: CodeType.FORGOT },
        verified: { equals: true },
        expiresAt: {
          greater_than: new Date().toISOString(),
        },
      },
    })
    if (!codeDoc) {
      return NextResponse.json(
        {
          error: i18n.t('authentication:invalidOrExpired', {
            token: i18n.t('authentication:otpCode'),
          }),
        },
        { status: 400 },
      )
    }
    await payload.db.deleteOne({ collection: 'opt-codes', where: { id: { equals: codeDoc.id } } })

    const hash = await generatePasswordHashWithSalt(password, userDoc.salt!)
    await payload.db.updateOne({
      collection: 'users',
      data: hash,
      where: { email: { equals: email } },
    })

    return NextResponse.json(
      { message: i18n.t('authentication:passwordResetSuccessfully') },
      { status: 200 },
    )
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Server Error' }, { status: 500 })
  }
}
