import { getNextRequestI18n } from '@payloadcms/next/utilities'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import type { TranslationsKeys, TranslationsObject } from '@/languages/translations'
import { getMeUser } from '@/utilities/getMeUser'
import buildConfig from '@payload-config'

export async function POST(req: NextRequest) {
  try {
    const config = await buildConfig
    const i18n = await getNextRequestI18n<TranslationsObject, TranslationsKeys>({ config })

    const body = await req.json()
    const { userId } = body
    if (!userId) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'userId' }) },
        { status: 400 },
      )
    }

    const { user } = await getMeUser()
    if (!user || user.id !== userId) {
      return NextResponse.json({ error: i18n.t('error:unauthorized') }, { status: 401 })
    }

    const payload = await getPayload({ config })

    const transactionID = await payload.db.beginTransaction()
    if (transactionID === null) throw new Error()

    try {
      await payload.db.deleteMany({
        collection: 'orders',
        where: { userId: { equals: user.id } },
        req: { transactionID },
      })
      await payload.db.deleteMany({
        collection: 'user-order-itineraries',
        where: { userId: { equals: user.id } },
        req: { transactionID },
      })
      await payload.db.deleteMany({
        collection: 'redemption-codes',
        where: { userId: { equals: user.id } },
        req: { transactionID },
      })
      await payload.db.deleteOne({
        collection: 'carts',
        where: { userId: { equals: user.id } },
        req: { transactionID },
      })

      await payload.db.deleteOne({
        collection: 'users',
        where: { id: { equals: user.id } },
        req: { transactionID },
      })

      // Commit the transaction
      await payload.db.commitTransaction(transactionID)
    } catch (error) {
      // Rollback the transaction
      await payload.db.rollbackTransaction(transactionID)

      throw error
    }

    return NextResponse.json({ message: i18n.t('general:accountCancelled') }, { status: 200 })
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Server Error' }, { status: 500 })
  }
}
