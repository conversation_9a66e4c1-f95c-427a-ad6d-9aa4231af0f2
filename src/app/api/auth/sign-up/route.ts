import { getNextRequestI18n } from '@payloadcms/next/utilities'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import isEmail from 'validator/es/lib/isEmail'

import { CodeType } from '@/enums/code-type'
import { EmailTemplateType } from '@/enums/email-template-type'
import type { TranslationsKeys, TranslationsObject } from '@/languages/translations'
import { InvitationCode } from '@/payload-types'
import { generatePasswordHashWithoutSalt } from '@/utilities/generatePasswordSaltHash'
import { generateRandomUUID } from '@/utilities/generateRandomCode'
import { getEmailTemplate } from '@/utilities/getEmailTemplate'
import buildConfig from '@payload-config'

export async function POST(req: NextRequest) {
  try {
    const config = await buildConfig
    const i18n = await getNextRequestI18n<TranslationsObject, TranslationsKeys>({ config })

    const body = await req.json()
    const { email, password, invitationCode } = body
    if (!isEmail(email)) {
      return NextResponse.json({ error: i18n.t('validation:emailAddress') }, { status: 400 })
    }
    if (!password) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'password' }) },
        { status: 400 },
      )
    }

    const payload = await getPayload({ config })
    const userCount = await payload.db.count({
      collection: 'users',
      where: { email: { equals: email } },
    })
    if (userCount.totalDocs > 0) {
      return NextResponse.json(
        { error: i18n.t('error:userEmailAlreadyRegistered') },
        { status: 400 },
      )
    }

    let invitationCodeDoc: InvitationCode | null = null
    if (invitationCode) {
      invitationCodeDoc = await payload.db.findOne({
        collection: 'invitation-codes',
        where: {
          numbers: { equals: invitationCode },
          expiresAt: { greater_than: new Date().toISOString() },
        },
      })
      if (!invitationCodeDoc) {
        return NextResponse.json(
          {
            error: i18n.t('error:notFound', {
              token: i18n.t('collection:invitationCode'),
            }),
          },
          { status: 400 },
        )
      } else if (invitationCodeDoc.currentUsage >= invitationCodeDoc.maxUsage) {
        return NextResponse.json(
          {
            error: i18n.t('error:usageLimitReached', {
              resource: i18n.t('collection:invitationCode'),
            }),
          },
          { status: 400 },
        )
      }
    }

    const codeDoc = await payload.db.findOne({
      collection: 'opt-codes',
      where: {
        email: { equals: email },
        type: { equals: CodeType.REGISTER },
        verified: { equals: true },
        expiresAt: {
          greater_than: new Date().toISOString(),
        },
      },
    })
    if (!codeDoc) {
      return NextResponse.json(
        {
          error: i18n.t('authentication:invalidOrExpired', {
            token: i18n.t('authentication:otpCode'),
          }),
        },
        { status: 400 },
      )
    }
    await payload.db.deleteOne({ collection: 'opt-codes', where: { id: { equals: codeDoc.id } } })

    const hashSalt = await generatePasswordHashWithoutSalt(password)
    const userData = { email, name: email, ...hashSalt }
    if (invitationCodeDoc) {
      Object.assign(userData, { invitationCodeId: invitationCodeDoc.id })
    }
    const user = await payload.db.create({
      collection: 'users',
      data: userData,
    })
    await payload.db.create({
      collection: 'carts',
      data: { userId: user.id },
    })
    if (invitationCodeDoc) {
      await payload.db.updateOne({
        collection: 'invitation-codes',
        id: invitationCodeDoc.id,
        data: { currentUsage: invitationCodeDoc.currentUsage + 1 },
      })
      await payload.db.create({
        collection: 'vouchers',
        data: {
          userId: user.id,
          invitationCodeId: invitationCodeDoc.id,
          discount: invitationCodeDoc.discount,
          expiresAt: invitationCodeDoc.expiresAt,
        },
      })
    }

    const loginBody = await payload.login({
      collection: 'users',
      context: { ignoreDashboardAccessControl: true },
      data: { email, password },
    })

    const numbers = generateRandomUUID()
    await payload.db.create({
      collection: 'redemption-codes',
      data: { userId: user.id, numbers },
    })

    const html = getEmailTemplate(EmailTemplateType.REDEEMCODE, {
      language: i18n.language,
      variables: { CODE: numbers, TITLE: i18n.t('collection:redemptionCode') },
    })
    await payload.sendEmail({
      from: `"${payload.email.defaultFromName}" <${payload.email.defaultFromAddress}>`,
      to: email,
      subject: i18n.t('collection:redemptionCode'),
      html,
    })

    return NextResponse.json(loginBody, { status: 200 })
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Server Error' }, { status: 500 })
  }
}
