import { getNextRequestI18n } from '@payloadcms/next/utilities'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import type { TranslationsKeys, TranslationsObject } from '@/languages/translations'
import { User } from '@/payload-types'
import { generatePasswordHashWithSalt } from '@/utilities/generatePasswordSaltHash'
import { getMeUser } from '@/utilities/getMeUser'
import buildConfig from '@payload-config'

export async function POST(req: NextRequest) {
  try {
    const config = await buildConfig
    const i18n = await getNextRequestI18n<TranslationsObject, TranslationsKeys>({ config })

    const { user } = await getMeUser()
    if (!user) {
      return NextResponse.json({ error: i18n.t('error:unauthorized') }, { status: 401 })
    }

    const body = await req.json()
    const { oldpassword, password, confirm } = body
    if (!oldpassword) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'oldpassword' }) },
        { status: 400 },
      )
    }
    if (!password) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'password' }) },
        { status: 400 },
      )
    }
    if (!confirm) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'confirm' }) },
        { status: 400 },
      )
    }
    if (password !== confirm) {
      return NextResponse.json(
        { error: i18n.t('authentication:passwordsNotMatch') },
        { status: 400 },
      )
    }

    const payload = await getPayload({ config })
    const userDoc = await payload.db.findOne<User>({
      collection: 'users',
      select: { hash: true, salt: true },
      where: { id: { equals: user.id } },
    })
    if (!userDoc) {
      return NextResponse.json({ error: i18n.t('error:unauthorized') }, { status: 401 })
    }

    const { hash: oldHash } = await generatePasswordHashWithSalt(oldpassword, userDoc.salt!)
    if (oldHash !== userDoc.hash) {
      return NextResponse.json(
        { error: i18n.t('authentication:passwordIncorrect') },
        { status: 400 },
      )
    }

    const hash = await generatePasswordHashWithSalt(password, userDoc.salt!)
    await payload.db.updateOne({
      collection: 'users',
      data: hash,
      id: user.id,
    })

    return NextResponse.json(
      { message: i18n.t('authentication:passwordResetSuccessfully') },
      { status: 200 },
    )
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Server Error' }, { status: 500 })
  }
}
