import { getNextRequestI18n } from '@payloadcms/next/utilities'
import { cookies, headers } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import type { TranslationsKeys, TranslationsObject } from '@/languages/translations'
import { Voucher } from '@/payload-types'
import { stripe } from '@/stripe'
import { getMeUser } from '@/utilities/getMeUser'
import { getClientSideURL } from '@/utilities/getURL'
import buildConfig from '@payload-config'

export async function POST(req: NextRequest) {
  try {
    const config = await buildConfig
    const i18n = await getNextRequestI18n<TranslationsObject, TranslationsKeys>({ config })

    const body = await req.json()
    const { itineraryIds, cartId, voucherId } = body

    if (!cartId) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'cartId' }) },
        { status: 400 },
      )
    }
    if (!itineraryIds || !Array.isArray(itineraryIds) || itineraryIds.length === 0) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'itineraryIds' }) },
        { status: 400 },
      )
    }

    const { user } = await getMeUser()
    if (!user) {
      return NextResponse.json({ error: i18n.t('error:unauthorized') }, { status: 401 })
    }

    const payload = await getPayload({ config })

    const cartDoc = await payload.findByID({
      collection: 'carts',
      id: cartId,
      depth: 0,
      disableErrors: true,
    })
    if (!cartDoc) {
      return NextResponse.json(
        { error: i18n.t('error:notFound', { resource: i18n.t('collection:cart') }) },
        { status: 404 },
      )
    }

    if (cartDoc.userId !== user.id) {
      return NextResponse.json({ error: i18n.t('error:unauthorized') }, { status: 401 })
    }
    const containsInvalidItineraryId = itineraryIds.some(
      (itineraryId) => !cartDoc.items!.includes(itineraryId),
    )
    if (containsInvalidItineraryId) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'itineraryIds' }) },
        { status: 400 },
      )
    }
    const orderCount = await payload.db.count({
      collection: 'user-order-itineraries',
      where: { userId: { equals: user.id }, itineraryId: { in: itineraryIds } },
    })
    if (orderCount.totalDocs > 0) {
      return NextResponse.json(
        { error: i18n.t('error:alreadyPurchasedOrRedeemed') },
        { status: 400 },
      )
    }

    const { docs: itineraryDocs } = await payload.find({
      collection: 'itineraries',
      where: { id: { in: itineraryIds } },
    })
    if (itineraryDocs.length === 0 || itineraryDocs.length !== itineraryIds.length) {
      return NextResponse.json(
        { error: i18n.t('error:notFound', { resource: i18n.t('collection:itinerary') }) },
        { status: 404 },
      )
    }

    let productIds = itineraryDocs.map((itinerary) => itinerary.productId)
    if (typeof productIds[0] === 'string') {
      const { docs: productDocs } = await payload.find({
        collection: 'products',
        where: { id: { in: productIds } },
      })
      if (productDocs.length === 0 || productDocs.length !== productIds.length) {
        return NextResponse.json(
          { error: i18n.t('error:notFound', { resource: i18n.t('collection:product') }) },
          { status: 404 },
        )
      }
      productIds = productDocs
    }

    const metadata = { cartId, itineraryId: JSON.stringify(itineraryIds), userId: user.id }

    let voucherDoc: Voucher | null = null
    if (voucherId) {
      voucherDoc = await payload.findByID({
        collection: 'vouchers',
        id: voucherId,
      })
      Object.assign(metadata, { voucherId: voucherDoc.id })
    }

    // @ts-ignore
    const totalAmount = productIds.reduce((acc, product) => acc + product.unitAmount, 0)
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(totalAmount * (voucherDoc?.discount ?? 1)),
      // @ts-ignore
      currency: productIds[0].defaultPriceCurrency,
      automatic_payment_methods: {
        enabled: true,
      },
      metadata,
    })
    const [cookieStore, headersStore] = await Promise.all([cookies(), headers()])
    const token =
      headersStore.get('Authorization')?.split(' ')[1] || cookieStore.get('payload-token')?.value
    const locale = headersStore.get('accept-language') || i18n.language
    const url = `${getClientSideURL()}/checkout?itineraries=${itineraryIds.join(',')}&client_secret=${paymentIntent.client_secret}&locale=${locale}&token=${token}${voucherId ? '&voucherId=' + voucherId : ''}`

    return NextResponse.json({ data: { url } }, { status: 200 })
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Sever Error' }, { status: 500 })
  }
}
