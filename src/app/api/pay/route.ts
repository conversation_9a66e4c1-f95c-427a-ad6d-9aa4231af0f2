import { getNextRequestI18n } from '@payloadcms/next/utilities'
import { cookies, headers } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import type { TranslationsKeys, TranslationsObject } from '@/languages/translations'
import { Voucher } from '@/payload-types'
import { stripe } from '@/stripe'
import { getMeUser } from '@/utilities/getMeUser'
import { getClientSideURL } from '@/utilities/getURL'
import buildConfig from '@payload-config'

export async function POST(req: NextRequest) {
  try {
    const config = await buildConfig
    const i18n = await getNextRequestI18n<TranslationsObject, TranslationsKeys>({ config })

    const body = await req.json()
    const { itineraryId, voucherId } = body
    if (!itineraryId) {
      return NextResponse.json(
        { error: i18n.t('error:invalidRequestArgs', { args: 'itineraryId' }) },
        { status: 400 },
      )
    }

    const { user } = await getMeUser()
    if (!user) {
      return NextResponse.json({ error: i18n.t('error:unauthorized') }, { status: 401 })
    }

    const payload = await getPayload({ config })

    const itinerary = await payload.findByID({
      collection: 'itineraries',
      id: itineraryId,
      depth: 1,
      disableErrors: true,
    })
    if (!itinerary) {
      return NextResponse.json(
        { error: i18n.t('error:notFound', { resource: i18n.t('collection:itinerary') }) },
        { status: 404 },
      )
    }

    const orderCount = await payload.db.count({
      collection: 'user-order-itineraries',
      where: { userId: { equals: user.id }, itineraryId: { equals: itineraryId } },
    })
    if (orderCount.totalDocs > 0) {
      return NextResponse.json(
        { error: i18n.t('error:alreadyPurchasedOrRedeemed') },
        { status: 400 },
      )
    }

    let product = itinerary.productId
    if (typeof product === 'string') {
      const productDoc = await payload.findByID({
        collection: 'products',
        id: product,
      })
      if (!productDoc) {
        return NextResponse.json(
          { error: i18n.t('error:notFound', { resource: i18n.t('collection:product') }) },
          { status: 404 },
        )
      }
      product = productDoc
    }

    const metadata = { itineraryId: JSON.stringify([itineraryId]), userId: user.id }

    let voucherDoc: Voucher | null = null
    if (voucherId) {
      voucherDoc = await payload.findByID({
        collection: 'vouchers',
        id: voucherId,
      })
      Object.assign(metadata, { voucherId: voucherDoc.id })
    }

    const paymentIntent = await stripe.paymentIntents.create({
      // @ts-ignore
      amount: Math.round(product.unitAmount * (voucherDoc?.discount ?? 1)),
      // @ts-ignore
      currency: product.defaultPriceCurrency,
      automatic_payment_methods: {
        enabled: true,
      },
      metadata,
    })
    const [cookieStore, headersStore] = await Promise.all([cookies(), headers()])
    const token =
      headersStore.get('Authorization')?.split(' ')[1] || cookieStore.get('payload-token')?.value
    const locale = headersStore.get('accept-language') || i18n.language
    const url = `${getClientSideURL()}/checkout?itineraries=${itineraryId}&client_secret=${paymentIntent.client_secret}&locale=${locale}&token=${token}${voucherId ? '&voucherId=' + voucherId : ''}`

    return NextResponse.json({ data: { url } }, { status: 200 })
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Sever Error' }, { status: 500 })
  }
}
