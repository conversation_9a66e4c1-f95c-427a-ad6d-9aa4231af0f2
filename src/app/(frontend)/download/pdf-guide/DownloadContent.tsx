'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { Toaster } from '@/components/ui/toaster'
import { Download } from 'lucide-react'

export function DownloadContent() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDownloading, setIsDownloading] = useState(false)
  const searchParams = useSearchParams()
  const { toast } = useToast()

  // 验证 token
  useEffect(() => {
    const token = searchParams.get('token')
    if (!token) {
      setError('Invalid download link')
    }
    setIsLoading(false)
  }, [searchParams])

  const handleDownload = async () => {
    const token = searchParams.get('token')
    if (!token) return

    setIsDownloading(true)
    try {
      const res = await fetch(`/api/download/pdf-guide?token=${token}`)
      const data = await res.json()

      if (data.error) {
        throw new Error(data.error)
      }

      window.location.href = data.downloadUrl
      toast({
        title: 'Download started!',
        description: 'Your guide will be downloaded automatically.',
      })
    } catch (err: any) {
      toast({
        title: 'Download failed',
        description: err.message || 'Please try again later',
        variant: 'destructive',
      })
    } finally {
      setIsDownloading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="max-w-md w-full p-6 text-center">
        {isLoading ? (
          <div className="space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto" />
            <p>Preparing your download...</p>
          </div>
        ) : error ? (
          <div className="space-y-4">
            <h1 className="text-xl font-bold text-red-500">Download Failed</h1>
            <p>{error}</p>
            <Button onClick={() => (window.location.href = '/')} variant="outline">
              Return to Homepage
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            <h1 className="text-xl font-bold text-primary">Your Guide is Ready!</h1>
            <p>Click the button below to start your download.</p>
            <Button onClick={handleDownload} className="w-full" size="lg" disabled={isDownloading}>
              {isDownloading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Downloading...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Download Guide
                </>
              )}
            </Button>
            <p className="text-sm text-muted-foreground">
              The download will begin once you click the button.
            </p>
          </div>
        )}
      </div>
      <Toaster />
    </div>
  )
}
