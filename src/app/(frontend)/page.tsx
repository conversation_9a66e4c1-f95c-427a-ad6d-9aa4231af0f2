import LandingPage from '@/components/landing/LandingPage'

// 使用固定的时间戳，避免水合错误
const LAST_MODIFIED = '2025-01-27T00:00:00.000Z'

export default function Home() {
  return (
    <>
      <LandingPage />

      {/* 添加结构化数据以提升SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'WebApplication',
            name: 'EasyGo China',
            description:
              'Your Ultimate China Travel Companion - Discover authentic China with curated travel routes, local food recommendations, and cultural insights.',
            url: 'https://easygochina.vercel.app',
            applicationCategory: 'TravelApplication',
            operatingSystem: 'iOS, Android, Web',
            offers: {
              '@type': 'Offer',
              price: '0',
              priceCurrency: 'USD',
            },
            author: {
              '@type': 'Organization',
              name: 'EasyGo China Team',
            },
            dateModified: LAST_MODIFIED,
          }),
        }}
      />
    </>
  )
}
