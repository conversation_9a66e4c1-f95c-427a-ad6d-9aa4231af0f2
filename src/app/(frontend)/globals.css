@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: auto;
    font-weight: auto;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 240 5% 96%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 240 6% 90%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.2rem;

    --success: 196 52% 74%;
    --warning: 34 89% 85%;
    --error: 10 100% 86%;

    --color-brand-primary: #ab332f;
    --color-brand-secondary: #dc2626;
    --color-brand-orange: #ea580c;
    --color-brand-gold: #f59e0b;

    /* 阴影 */
    --shadow-brand: 0 10px 25px -3px rgba(171, 51, 47, 0.1), 0 4px 6px -2px rgba(171, 51, 47, 0.05);
    --shadow-brand-lg: 0 25px 50px -12px rgba(171, 51, 47, 0.25);
  }

  [data-theme='dark'] {
    --background: 0 0% 0%;
    --foreground: 210 40% 98%;

    --card: 0 0% 4%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 0, 0%, 15%, 0.5;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --success: 196 100% 14%;
    --warning: 34 51% 25%;
    --error: 10 39% 43%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground min-h-[100vh] flex flex-col;
  }
}

@layer utilities {
  .text-gradient-brand {
    background: linear-gradient(
      135deg,
      var(--color-brand-primary) 0%,
      var(--color-brand-secondary) 100%
    );
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .bg-gradient-brand {
    background: linear-gradient(
      135deg,
      var(--color-brand-primary) 0%,
      var(--color-brand-secondary) 100%
    );
  }

  .bg-gradient-warm {
    background: linear-gradient(
      135deg,
      var(--color-brand-primary) 0%,
      var(--color-brand-orange) 100%
    );
  }

  .bg-gradient-gold {
    background: linear-gradient(
      135deg,
      var(--color-brand-primary) 0%,
      var(--color-brand-gold) 100%
    );
  }

  .bg-pattern-subtle {
    background-image: radial-gradient(
        circle at 25% 25%,
        rgba(171, 51, 47, 0.03) 0%,
        transparent 50%
      ),
      radial-gradient(circle at 75% 75%, rgba(220, 38, 38, 0.03) 0%, transparent 50%),
      linear-gradient(45deg, rgba(171, 51, 47, 0.01) 25%, transparent 25%),
      linear-gradient(-45deg, rgba(220, 38, 38, 0.01) 25%, transparent 25%);
    background-size:
      60px 60px,
      80px 80px,
      20px 20px,
      20px 20px;
    background-position:
      0 0,
      40px 40px,
      0 0,
      10px 10px;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-20px);
    }
  }
}

html {
  opacity: 0;
}

html[data-theme='dark'],
html[data-theme='light'] {
  opacity: initial;
}
