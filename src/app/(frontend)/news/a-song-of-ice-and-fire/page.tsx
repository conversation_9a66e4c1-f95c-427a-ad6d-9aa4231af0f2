/* eslint-disable react/no-unescaped-entities */
'use client'

import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react'
import { Toaster } from '@/components/ui/toaster'

import useEmblaCarousel from 'embla-carousel-react'
import Autoplay from 'embla-carousel-autoplay'
import ContactInformation from '@/components/Section/ContactInformation'
import OurStory from '@/components/Section/OurStory'
import AppFeature from '@/components/Section/AppFeature'
import GetGift from '@/components/Section/GetGift'

export default function HarbinTourPage() {
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
    },
    [
      Autoplay({
        delay: 4000,
        stopOnInteraction: false,
        stopOnMouseEnter: true,
      }),
    ],
  )

  const scrollToContent = () => {
    window.scrollTo({
      top: window.innerHeight,
      behavior: 'smooth',
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-primary/2 to-white dark:from-slate-900 dark:to-slate-800">
      {/* Hero Section */}
      <section className="relative h-[100vh] flex flex-col items-center justify-between">
        {/* 移动端图片 */}
        <Image
          src="/images/harbin-ice-mobile.jpg"
          alt="Harbin Ice and Snow World"
          fill
          className="md:hidden !object-[80%]"
          priority
        />

        {/* 桌面端图片 */}
        <Image
          src="/images/harbin-ice-5.jpg"
          alt="Harbin Ice and Snow World"
          fill
          className="hidden md:block !object-[80%] md:!object-cover"
          priority
        />

        {/* 主要内容区域 */}
        <div className="relative z-10 text-center text-white max-w-4xl mx-auto px-4 space-y-8 mt-[50vh]">
          {/* 主标题 */}
          {/* <div className="space-y-2 animate-fade-in-up opacity-0">
            <h1 className="text-4xl md:text-5xl font-bold mb-4 flex flex-wrap items-center justify-center gap-x-3">
              A{' '}
              <span className="relative inline-block">
                <span className="relative z-10">Free</span>
                <span
                  className="absolute bottom-0 left-0 w-full h-[45%] bg-orange-500 -skew-x-6"
                  style={{ transform: 'skew(-6deg)' }}
                ></span>
              </span>{' '}
              Gift from{' '}
              <span className="text-primary/90 drop-shadow-[0_2px_2px_rgba(0,0,0,0.8)]">
                EasyGo China
              </span>
            </h1>
          </div> */}

          {/* 副标题 */}
          {/* <div className="space-y-6 animate-fade-in-up-1 opacity-0">
            <h2 className="text-2xl md:text-4xl font-semibold tracking-wide">
              Self-guided Tour of Harbin
              <div className="mt-2 font-bold italic text-3xl md:text-5xl bg-gradient-to-r from-white to-primary/90 bg-clip-text text-transparent drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]">
                "A Song of Ice and Fire"
              </div>
            </h2>
          </div> */}

          {/* 新的标题部分 */}
          <div className="flex flex-col items-center justify-center text-center space-y-9 mt-20">
            <h1 className="flex flex-col items-center gap-2">
              <div className="inline-flex items-baseline animate-fade-in-up">
                <span className="text-2xl md:text-7xl text-white font-bold relative px-3 py-1">
                  <span className="relative z-10">A Free Gift</span>
                  <span className="absolute bottom-0 left-0 w-full h-[2.8rem] bg-primary -z-[1]"></span>
                  <span className="text-lg md:text-2xl ml-2">From EasyGo China</span>
                </span>
              </div>
            </h1>

            <div className="space-y-2 animate-fade-in-up-1 opacity-0">
              <div className="text-xl md:text-2xl text-primary font-semibold">
                Self-guided Tour of Harbin
              </div>
              <div className="text-2xl md:text-3xl italic font-serif text-primary font-bold">
                "A Song of Ice and Fire"
              </div>
            </div>
          </div>
        </div>

        {/* 箭头指示器 */}
        <div className="relative z-10 mb-0 animate-fade-in-up-3 opacity-0 w-full flex justify-center">
          <button
            onClick={scrollToContent}
            className="group flex flex-col items-center gap-2 transition-transform hover:scale-110"
          >
            <span className="text-lg font-medium tracking-wider text-primary">Discover More</span>
            <div className="relative">
              <ChevronDown className="w-10 h-10 animate-bounce text-primary" strokeWidth={2.5} />
              <div className="absolute top-0 left-0 w-10 h-10 animate-ping-slow opacity-75">
                <ChevronDown className="w-full h-full text-primary" strokeWidth={2.5} />
              </div>
            </div>
          </button>
        </div>
      </section>

      {/* Description Section */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto space-y-16">
          {/* About This Tour Section */}
          <div className="prose prose-primary dark:prose-invert max-w-none">
            <h2 className="text-3xl text-center font-bold mb-6 dark:text-white">About This Gift</h2>
            <div className="space-y-6">
              <p className="text-lg dark:text-gray-300">
                This is a PDF-format one-day tour for Harbin. It’s not a guide but a self-paced tour
                that you can enjoy without a tour guide. The itinerary covers four main attractions:
                Daoli Market, Saint Sophia Cathedral, Central Avenue, and Harbin Ice and Snow World.
                It also features nearly 50 local delicacies and beverages, along with the renowned
                Northeast-style bathhouse experience, crafting a full day of joy for you. From
                marveling at breathtaking ice and snow art in temperatures as low as -20°C to
                finding warmth in a sauna heated to over 80°C, this tour promises unforgettable
                moments!
              </p>
            </div>

            {/*Tour Description Section*/}
            <div className="flex flex-col md:flex-row gap-8 mt-8">
              {/* 轮播图部分 */}
              <div className="md:w-1/2">
                <div className="relative group">
                  <div className="overflow-hidden rounded-lg" ref={emblaRef}>
                    <div className="flex">
                      {Array.from({ length: 6 }, (_, i) => (
                        <div key={i} className="relative min-w-full">
                          <div className="relative aspect-[4/3] overflow-hidden">
                            <Image
                              src={`/images/harbin-ice/ice-img${i + 1}.jpg`}
                              alt={`Harbin Ice Festival Scene ${i + 1}`}
                              fill
                              className="object-cover mt-0 hover:scale-105 transition-transform duration-500"
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="icon"
                    className="absolute left-4 top-1/2 -translate-y-1/2 rounded-full bg-background/80 backdrop-blur-sm z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    onClick={() => emblaApi?.scrollPrev()}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    className="absolute right-4 top-1/2 -translate-y-1/2 rounded-full bg-background/80 backdrop-blur-sm z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    onClick={() => emblaApi?.scrollNext()}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* 文字描述部分 */}
              <div className="md:w-1/2">
                <div className="bg-primary/5 dark:bg-primary/10 p-6 rounded-lg h-full flex items-center">
                  <p className="text-xl text-primary/80 dark:text-primary/90">
                    Crafting this tour required dozens of work hours, countless phone calls,
                    thorough research and genuine experience. Now, we're giving it away for free to
                    ensure you have an incredible holiday in China while helping us build
                    anticipation for the launch of our app in February.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* How to Get Your Gift Section */}
          <GetGift formId={process.env.NEXT_PUBLIC_ICE_AND_FIRE_FORM_ID} />

          {/* App Features Section */}
          <AppFeature />

          {/* Our Story Section */}
          <OurStory />

          {/* Contact Information Section */}
          <ContactInformation />
        </div>
      </section>

      {/* 添加 Toaster 组件 */}
      <Toaster />
    </div>
  )
}
