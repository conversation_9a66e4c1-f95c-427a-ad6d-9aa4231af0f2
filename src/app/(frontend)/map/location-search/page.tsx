'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Map, ArrowLeft, Co<PERSON>, Check } from 'lucide-react'
import { Card } from '@/components/ui/card'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { TooltipProvider } from '@radix-ui/react-tooltip'

const CITIES = [
  { label: '全国', value: 'all' },
  { label: '北京', value: 'beijing' },
  { label: '上海', value: 'shanghai' },
  { label: '广州', value: 'guangzhou' },
  { label: '深圳', value: 'shenzhen' },
]

interface Location {
  name: string
  address: string
  tel?: string
  location: [number, number]
}

declare global {
  interface Window {
    AMap: any
  }
}

interface SearchResult {
  id: string
  name: string
  address: string
  tel?: string
  type?: string
  location: {
    lat: number
    lng: number
  }
}

export default function LocationSearchPage() {
  const mapRef = useRef<HTMLDivElement>(null)
  const amapRef = useRef<any>(null)
  const markerRef = useRef<any>(null)
  const [searchValue, setSearchValue] = useState('')
  const [selectedCity, setSelectedCity] = useState('all')
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null)
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const searchTimeout = useRef<NodeJS.Timeout | null>(null)
  const [copiedField, setCopiedField] = useState<string | null>(null)

  useEffect(() => {
    if (mapRef.current && !amapRef.current) {
      amapRef.current = new window.AMap.Map(mapRef.current, {
        zoom: 11,
        center: [116.397428, 39.90923],
        resizeEnable: true,
      })
    }

    return () => {
      if (amapRef.current) {
        amapRef.current.destroy()
      }
    }
  }, [])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setOpen(false)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const handleSearch = async (value: string) => {
    setInputValue(value)

    // 清除之前的定时器
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current)
    }

    // 设置新的定时器，延迟搜索
    searchTimeout.current = setTimeout(() => {
      if (!value.trim()) {
        setSearchResults([])
        return
      }
      setLoading(true)

      const placeSearch = new (window as any).AMap.PlaceSearch({
        city: selectedCity === 'all' ? '全国' : selectedCity,
        citylimit: selectedCity !== 'all',
      })

      placeSearch.search(value, (status: string, result: any) => {
        setLoading(false)
        if (status === 'complete' && result.info === 'OK') {
          setSearchResults(result.poiList.pois)
          setOpen(true)
        }
      })
    }, 300) // 300ms 防抖
  }

  const handleSelectLocation = (poi: any) => {
    const { location, name, address, tel } = poi

    if (markerRef.current) {
      markerRef.current.setMap(null)
    }

    markerRef.current = new window.AMap.Marker({
      position: [location.lng, location.lat],
      map: amapRef.current,
    })

    amapRef.current.setCenter([location.lng, location.lat])
    amapRef.current.setZoom(15)

    setSelectedLocation({
      name,
      address,
      location: [location.lng, location.lat],
      tel,
    })

    setOpen(false)
  }

  const handleBack = () => {
    setOpen(true)
    if (searchResults.length === 0 && inputValue) {
      handleSearch(inputValue)
    }
  }

  const handleCopy = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(field)
      setTimeout(() => setCopiedField(null), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  return (
    <div className="h-[80vh] flex">
      <Card className="w-[400px] flex-shrink-0 border-r rounded-none">
        <div className="p-6 space-y-6">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Map className="h-6 w-6" />
            位置搜索
          </h1>

          <div className="space-y-4">
            <Select value={selectedCity} onValueChange={setSelectedCity}>
              <SelectTrigger>
                <SelectValue placeholder="选择城市" />
              </SelectTrigger>
              <SelectContent>
                {CITIES.map((city) => (
                  <SelectItem key={city.value} value={city.value}>
                    {city.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="relative">
              <Command
                className="rounded-lg border shadow-md overflow-visible"
                shouldFilter={false}
              >
                <CommandInput
                  placeholder="搜索地点..."
                  value={inputValue}
                  onValueChange={handleSearch}
                  onKeyDown={(e) => {
                    if (e.key === 'Escape') {
                      e.preventDefault()
                      setOpen(false)
                    }
                  }}
                />
                {open && (
                  <div className="relative">
                    <CommandList className="absolute w-full bg-popover rounded-lg border shadow-md mt-1 max-h-[400px] overflow-y-auto">
                      <CommandEmpty className="py-6 text-center text-sm">
                        未找到相关地点
                      </CommandEmpty>
                      <CommandGroup>
                        {searchResults.map((poi) => (
                          <CommandItem
                            key={poi.id}
                            value={poi.name}
                            onSelect={() => {
                              handleSelectLocation(poi)
                              setOpen(false)
                              setInputValue(poi.name)
                            }}
                            className="cursor-pointer aria-selected:bg-accent py-3"
                          >
                            <div className="flex flex-col gap-1.5 w-full">
                              <div className="flex items-start justify-between gap-2">
                                <div className="font-medium">{poi.name}</div>
                                {poi.type && (
                                  <span className="text-xs px-2 py-0.5 rounded-full bg-muted text-muted-foreground">
                                    {poi.type}
                                  </span>
                                )}
                              </div>
                              <div className="text-sm text-muted-foreground space-y-1">
                                <div className="flex items-start gap-2">
                                  <span className="flex-shrink-0">地址：</span>
                                  <span>{poi.address || '暂无地址信息'}</span>
                                </div>
                                {poi.tel && (
                                  <div className="flex items-start gap-2">
                                    <span className="flex-shrink-0">电话：</span>
                                    <span>{poi.tel}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </div>
                )}
              </Command>
            </div>
          </div>

          {selectedLocation && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <button
                  onClick={handleBack}
                  className="hover:bg-muted p-1 rounded-md transition-colors"
                >
                  <ArrowLeft className="h-5 w-5" />
                </button>
                <h3 className="font-medium">已选择位置</h3>
              </div>
              <Card className="p-4 space-y-2">
                <TooltipProvider>
                  <div className="flex items-start gap-2 group">
                    <span className="font-medium flex-shrink-0">名称：</span>
                    <span className="flex-1">{selectedLocation.name}</span>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          onClick={() => handleCopy(selectedLocation.name, 'name')}
                          className="opacity-0 group-hover:opacity-100 hover:bg-muted p-1 rounded-md transition-all"
                        >
                          {copiedField === 'name' ? (
                            <Check className="h-4 w-4 text-green-500" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{copiedField === 'name' ? '已复制！' : '点击复制'}</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>

                  <div className="flex items-start gap-2 group">
                    <span className="font-medium flex-shrink-0">地址：</span>
                    <span className="flex-1">{selectedLocation.address || '暂无地址信息'}</span>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          onClick={() => handleCopy(selectedLocation.address || '', 'address')}
                          className="opacity-0 group-hover:opacity-100 hover:bg-muted p-1 rounded-md transition-all"
                        >
                          {copiedField === 'address' ? (
                            <Check className="h-4 w-4 text-green-500" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{copiedField === 'address' ? '已复制！' : '点击复制'}</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>

                  {selectedLocation.tel && (
                    <div className="flex items-start gap-2 group">
                      <span className="font-medium flex-shrink-0">电话：</span>
                      <span className="flex-1">{selectedLocation.tel}</span>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button
                            onClick={() => handleCopy(selectedLocation.tel || '', 'tel')}
                            className="opacity-0 group-hover:opacity-100 hover:bg-muted p-1 rounded-md transition-all"
                          >
                            {copiedField === 'tel' ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{copiedField === 'tel' ? '已复制！' : '点击复制'}</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  )}

                  <div className="space-y-1">
                    <div className="flex items-start gap-2 group">
                      <span className="font-medium flex-shrink-0">经度：</span>
                      <span className="flex-1">{selectedLocation.location[0]}</span>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button
                            onClick={() => handleCopy(String(selectedLocation.location[0]), 'lng')}
                            className="opacity-0 group-hover:opacity-100 hover:bg-muted p-1 rounded-md transition-all"
                          >
                            {copiedField === 'lng' ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{copiedField === 'lng' ? '已复制！' : '点击复制'}</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>

                    <div className="flex items-start gap-2 group">
                      <span className="font-medium flex-shrink-0">纬度：</span>
                      <span className="flex-1">{selectedLocation.location[1]}</span>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button
                            onClick={() => handleCopy(String(selectedLocation.location[1]), 'lat')}
                            className="opacity-0 group-hover:opacity-100 hover:bg-muted p-1 rounded-md transition-all"
                          >
                            {copiedField === 'lat' ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{copiedField === 'lat' ? '已复制！' : '点击复制'}</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                </TooltipProvider>
              </Card>
            </div>
          )}
        </div>
      </Card>

      <div ref={mapRef} className="flex-1" />
    </div>
  )
}
