'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Map, Navigation2, Car, Bike } from 'lucide-react'
import { Card } from '@/components/ui/card'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import SearchLocation from '@/components/SearchLocation'
import RouteDetails from '@/components/RouteDetails'
import useAMapLoader from '@/hooks/useAMapLoader'

type TransportMode = 'driving' | 'walking' | 'bicycling' | 'transit'
type Location = { name: string; location: [number, number] }

function App() {
  const mapRef = useRef<HTMLDivElement>(null)
  const amapRef = useRef<any>(null)
  const markersRef = useRef<any[]>([])
  const polylineRef = useRef<any>(null)
  const [mode, setMode] = useState<TransportMode>('driving')
  const [startPoint, setStartPoint] = useState<Location | null>(null)
  const [endPoint, setEndPoint] = useState<Location | null>(null)
  const [route, setRoute] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const isMapLoaded = useAMapLoader()

  useEffect(() => {
    if (isMapLoaded && mapRef.current && !amapRef.current) {
      amapRef.current = new window.AMap.Map(mapRef.current, {
        zoom: 11,
        center: [116.397428, 39.90923],
        resizeEnable: true,
        lang: 'en',
      })
    }

    return () => {
      if (amapRef.current) {
        amapRef.current.destroy()
      }
    }
  }, [isMapLoaded])

  const clearMapOverlays = () => {
    if (amapRef.current) {
      if (polylineRef.current) {
        amapRef.current.remove(polylineRef.current)
        polylineRef.current = null
      }
      markersRef.current.forEach((marker) => marker.remove())
      markersRef.current = []
    }
  }

  const addMarker = (location: [number, number], title: string, color: string) => {
    if (!amapRef.current) return

    const marker = new window.AMap.Marker({
      position: new window.AMap.LngLat(...location),
      title,
      animation: 'AMAP_ANIMATION_DROP',
      icon: new window.AMap.Icon({
        size: new window.AMap.Size(25, 34),
        imageSize: new window.AMap.Size(25, 34),
        image: `https://webapi.amap.com/theme/v1.3/markers/n/mark_${color}.png`,
      }),
    })

    marker.setMap(amapRef.current)
    markersRef.current.push(marker)
    return marker
  }

  const drawRoute = (path: any[]) => {
    if (!amapRef.current) return

    if (polylineRef.current) {
      amapRef.current.remove(polylineRef.current)
    }

    polylineRef.current = new window.AMap.Polyline({
      path: path.map((p) => new window.AMap.LngLat(p.lng || p[0], p.lat || p[1])),
      isOutline: true,
      outlineColor: '#ffeeee',
      borderWeight: 2,
      strokeWeight: 5,
      strokeColor: '#0091ff',
      lineJoin: 'round',
    })

    polylineRef.current.setMap(amapRef.current)
  }

  useEffect(() => {
    if (!isMapLoaded) return

    clearMapOverlays()

    if (startPoint) {
      addMarker(startPoint.location, startPoint.name, 'b')
    }
    if (endPoint) {
      addMarker(endPoint.location, endPoint.name, 'r')
    }

    if (startPoint && endPoint) {
      calculateRoute()
    }
  }, [startPoint, endPoint, mode, isMapLoaded])

  const calculateRoute = async () => {
    if (!startPoint || !endPoint || !isMapLoaded || !amapRef.current) return

    setLoading(true)

    const routingMethod =
      mode === 'driving'
        ? window.AMap.Driving
        : mode === 'walking'
          ? window.AMap.Walking
          : mode === 'bicycling'
            ? window.AMap.Riding
            : window.AMap.Transfer

    const routing = new routingMethod({
      map: amapRef.current,
      policy: window.AMap.DrivingPolicy.LEAST_TIME,
      panel: false,
    })

    routing.search(
      new window.AMap.LngLat(...startPoint.location),
      new window.AMap.LngLat(...endPoint.location),
      (status: string, result: any) => {
        setLoading(false)

        if (status === 'complete') {
          const route = result.routes[0]
          setRoute(route)

          const path = route.steps.map((step: any) => step.path).flat()
          console.log(path)
          drawRoute(path)

          amapRef.current.setFitView([
            new window.AMap.Marker({ position: new window.AMap.LngLat(...startPoint.location) }),
            new window.AMap.Marker({ position: new window.AMap.LngLat(...endPoint.location) }),
          ])
        }
      },
    )
  }

  if (!isMapLoaded) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-lg">加载地图中...</div>
      </div>
    )
  }

  return (
    <div className="h-full flex">
      <Card className="w-[400px] flex-shrink-0 border-r rounded-none">
        <div className="p-6 space-y-6">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Map className="h-6 w-6" />
            路径规划
          </h1>

          <Tabs value={mode} onValueChange={(v) => setMode(v as TransportMode)} className="w-full">
            <TabsList className="grid grid-cols-4 w-full">
              <TabsTrigger
                value="driving"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <Car className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger
                value="transit"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <Navigation2 className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger
                value="walking"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <svg
                  className="h-4 w-4"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path d="M12 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4zM7 21h3l2-4 2 4h3M8 12h8M12 8v4" />
                </svg>
              </TabsTrigger>
              <TabsTrigger
                value="bicycling"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <Bike className="h-4 w-4" />
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="space-y-4">
            <SearchLocation
              placeholder="起点"
              onSelect={(location) => setStartPoint(location)}
              value={startPoint?.name}
            />
            <SearchLocation
              placeholder="终点"
              onSelect={(location) => setEndPoint(location)}
              value={endPoint?.name}
            />
          </div>

          {loading ? (
            <div className="text-center py-4 text-muted-foreground">正在规划路线...</div>
          ) : (
            route && <RouteDetails route={route} mode={mode} />
          )}
        </div>
      </Card>

      <div ref={mapRef} className="flex-1" />
    </div>
  )
}

export default App
