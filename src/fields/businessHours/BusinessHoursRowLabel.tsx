'use client'

import React from 'react'
import { useRowLabel } from '@payloadcms/ui'

const dayLabels = {
  monday: { en: 'Monday', zh: '星期一' },
  tuesday: { en: 'Tuesday', zh: '星期二' },
  wednesday: { en: 'Wednesday', zh: '星期三' },
  thursday: { en: 'Thursday', zh: '星期四' },
  friday: { en: 'Friday', zh: '星期五' },
  saturday: { en: 'Saturday', zh: '星期六' },
  sunday: { en: 'Sunday', zh: '星期日' }
}

export const BusinessHoursRowLabel: React.FC = () => {
  const { data } = useRowLabel<{ day?: keyof typeof dayLabels }>()
  
  // Determine the current locale, defaulting to 'en'
  const locale = typeof window !== 'undefined' 
    ? (window.localStorage.getItem('payload-language') || 'zh') 
    : 'zh'

  const dayLabel = data?.day 
    ? dayLabels[data.day][locale]
    : 'Business Hours'

  return <>{dayLabel}</>
}