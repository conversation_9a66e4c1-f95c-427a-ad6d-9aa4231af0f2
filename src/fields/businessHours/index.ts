import type { Field } from 'payload'

const daysOfWeek = [
  {
    label: {
      en: 'Monday',
      zh: '星期一',
    },
    value: 'monday',
  },
  {
    label: {
      en: 'Tuesday',
      zh: '星期二',
    },
    value: 'tuesday',
  },
  {
    label: {
      en: 'Wednesday',
      zh: '星期三',
    },
    value: 'wednesday',
  },
  {
    label: {
      en: 'Thursday',
      zh: '星期四',
    },
    value: 'thursday',
  },
  {
    label: {
      en: 'Friday',
      zh: '星期五',
    },
    value: 'friday',
  },
  {
    label: {
      en: 'Saturday',
      zh: '星期六',
    },
    value: 'saturday',
  },
  {
    label: {
      en: 'Sunday',
      zh: '星期日',
    },
    value: 'sunday',
  },
]

export const BusinessHoursField: Field = {
  name: 'businessHours',
  type: 'array',
  label: {
    en: 'Business Hours',
    zh: '营业时间',
  },
  admin: {
    condition: (data) => true,
    components: {
      RowLabel: '@/fields/businessHours/BusinessHoursRowLabel#BusinessHoursRowLabel',
    },
  },
  fields: [
    {
      name: 'day',
      type: 'select',
      options: daysOfWeek,
      required: true,
      label: {
        en: 'Day of Week',
        zh: '星期',
      },
    },
    {
      name: 'isOpen',
      type: 'checkbox',
      label: {
        en: 'Open',
        zh: '营业',
      },
      defaultValue: true,
    },
    {
      name: 'timeSlots',
      type: 'array',
      label: {
        en: 'Time Slots',
        zh: '营业时间段',
      },
      admin: {
        condition: (data, subData) => {
          return subData.isOpen
        },
      },
      fields: [
        {
          name: 'timeRange',
          type: 'group',
          label: {
            en: 'Business Hours',
            zh: '营业时间',
          },
          admin: {
            style: {
              display: 'flex',
              gap: '10px',
            },
          },
          fields: [
            {
              type: 'row',
              fields: [
                {
                  name: 'openTime',
                  type: 'date',
                  admin: {
                    date: {
                      pickerAppearance: 'timeOnly',
                      displayFormat: 'HH:mm a',
                      timeFormat: 'HH:mm a',
                    },
                    width: '50%',
                  },
                  label: {
                    en: 'Open',
                    zh: '开始',
                  },
                },
                {
                  name: 'closeTime',
                  type: 'date',
                  admin: {
                    date: {
                      pickerAppearance: 'timeOnly',
                      displayFormat: 'HH:mm a',
                      timeFormat: 'HH:mm a',
                    },
                    width: '50%',
                  },
                  label: {
                    en: 'Close',
                    zh: '结束',
                  },
                },
              ],
            },
          ],
        },
      ],
    },
  ],
}
