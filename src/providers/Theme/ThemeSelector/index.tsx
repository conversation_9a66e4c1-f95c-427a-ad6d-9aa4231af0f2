'use client'

import * as React from 'react'
import { LaptopIcon, MoonIcon, SunIcon } from 'lucide-react'
import { useTheme } from '..'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

const DATA = [
  {
    value: 'system',
    icon: LaptopIcon,
    label: '系统',
  },
  {
    value: 'light',
    icon: SunIcon,
    label: '浅色',
  },
  {
    value: 'dark',
    icon: MoonIcon,
    label: '深色',
  },
]

export const ThemeSelector: React.FC = () => {
  const { setTheme, theme } = useTheme()
  const themeValue = theme || 'system'

  const handleChangeTheme = (e: React.ChangeEvent<HTMLInputElement>): void => {
    if (e.target.value === 'system') {
      setTheme(null)
    } else {
      setTheme(e.target.value as 'light' | 'dark')
    }
  }

  return (
    <TooltipProvider>
      <div className="flex w-fit rounded-full border bg-background p-0.5">
        {DATA.map(({ value, icon: Icon, label }) => (
          <span key={value} className="h-full">
            <input
              className="peer sr-only"
              type="radio"
              id={`theme-switch-${value}`}
              value={value}
              checked={themeValue === value}
              onChange={handleChangeTheme}
            />
            <label
              htmlFor={`theme-switch-${value}`}
              className="flex size-6 cursor-pointer items-center justify-center rounded-full text-muted-foreground peer-checked:bg-accent peer-checked:text-foreground"
              aria-label={`${label}主题`}
            >
              <Tooltip delayDuration={600}>
                <TooltipTrigger asChild>
                  <Icon className="size-4 shrink-0" />
                </TooltipTrigger>
                <TooltipContent>{label}</TooltipContent>
              </Tooltip>
            </label>
          </span>
        ))}
      </div>
    </TooltipProvider>
  )
}
