import type { StripeWebhook<PERSON>andler } from '@payloadcms/plugin-stripe/types'
import type Stripe from 'stripe'

export const paymentIntentSucceeded: StripeWebhookHandler<{
  data: {
    object: Stripe.PaymentIntent
  }
}> = async (args) => {
  const { event, payload } = args

  const { id: stripePaymentIntentID, amount, currency, metadata } = event.data.object

  const { cartId, itineraryId, userId, voucherId } = metadata

  try {
    const itineraryIds = JSON.parse(itineraryId)

    if (cartId) {
      const cartDoc = await payload.findByID({
        collection: 'carts',
        id: cartId,
        depth: 0,
        disableErrors: true,
      })
      if (!cartDoc) {
        throw new Error(`Cart ${cartId} not found`)
      }
      if (cartDoc.userId !== userId) {
        throw new Error(`Cart ${cartId} does not belong to user ${userId}`)
      }
      const remainingItineraryIds = cartDoc.items!.filter(
        (id) => !itineraryIds.includes(id as string),
      )
      await payload.db.updateOne({
        collection: 'carts',
        id: cartId,
        data: {
          items: remainingItineraryIds,
        },
      })
    }

    const orderData = {
      userId,
      stripePaymentIntentID,
      itineraryId: itineraryIds,
      amount,
      currency,
    }
    if (voucherId) {
      Object.assign(orderData, { voucherId })

      await payload.db.updateOne({
        collection: 'vouchers',
        id: voucherId,
        data: { used: true },
      })
    }
    const order = await payload.db.create({
      collection: 'orders',
      data: orderData,
    })
    await Promise.all(
      itineraryIds.map(async (itineraryId: string) => {
        await payload.db.create({
          collection: 'user-order-itineraries',
          data: {
            userId,
            itineraryId,
            orderId: order.id,
          },
        })
      }),
    )

    console.log('Create an order', order.id)
  } catch (error) {
    console.error('Catch error while creating an order', error.toString())
  }
}
