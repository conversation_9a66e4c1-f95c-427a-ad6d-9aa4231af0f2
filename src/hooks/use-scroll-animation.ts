import { useRef, useEffect } from 'react'

interface ScrollAnimationOptions {
  delay?: number
  duration?: number
  translateY?: number
  scale?: [number, number]
  threshold?: number
}

export function useScrollAnimation({
  delay = 0,
  duration = 1000,
  translateY = 50,
  scale = [1, 1],
  threshold = 0.1,
}: ScrollAnimationOptions = {}) {
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setTimeout(() => {
              element.style.transition = `transform ${duration}ms cubic-bezier(0.25, 0.1, 0.25, 1), opacity ${duration}ms cubic-bezier(0.25, 0.1, 0.25, 1)`
              element.style.opacity = '1'
              element.style.transform = 'translateY(0) scale(1)'
            }, delay)
          }
        })
      },
      { threshold },
    )

    // Set initial styles
    element.style.opacity = '0'
    element.style.transform = `translateY(${translateY}px) scale(${scale[0]})`

    observer.observe(element)

    return () => {
      observer.disconnect()
    }
  }, [delay, duration, translateY, scale, threshold])

  return ref
}
