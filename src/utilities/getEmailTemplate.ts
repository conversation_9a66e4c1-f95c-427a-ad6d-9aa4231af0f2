import fs from 'fs'
import path from 'path'

import { EmailTemplateType } from '@/enums/email-template-type'

type EmailTemplateOptions = {
  language?: string
  variables?: Record<string, string | number>
}

export function getEmailTemplate(
  type: EmailTemplateType,
  options: EmailTemplateOptions = {},
): string {
  const { language, variables } = options

  const filename = language ? `${type}.${language}.html` : `${type}.html`
  const filepath = path.join(process.cwd(), 'public/html', filename)
  let content = fs.readFileSync(filepath, 'utf8')

  if (variables) {
    Object.entries(variables).forEach(([key, value]) => {
      content = content.replace(new RegExp(`{{${key}}}`, 'g'), value.toString())
    })
  }

  return content
}
