import crypto from 'crypto'

function fillNumberWithZero(num: number, len: number): number {
  return parseInt(`${num}`.padEnd(len, '0'))
}

export function generateRandomCode(length: number = 4): string {
  return Math.floor(
    fillNumberWithZero(1, length) + Math.random() * fillNumberWithZero(9, length),
  ).toString()
}

export function generateRandomUUID() {
  return crypto.randomUUID().toUpperCase()
}
