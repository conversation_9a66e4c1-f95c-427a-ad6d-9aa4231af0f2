import crypto from 'crypto'

function randomBytes() {
  return new Promise<Buffer>((resolve, reject) =>
    crypto.randomBytes(32, (err, saltBuffer) => (err ? reject(err) : resolve(saltBuffer))),
  )
}
function pbkdf2Promisified(password: string, salt: string) {
  return new Promise<Buffer>((resolve, reject) =>
    crypto.pbkdf2(password, salt, 25000, 512, 'sha256', (err, hashRaw) =>
      err ? reject(err) : resolve(hashRaw),
    ),
  )
}

export async function generatePasswordHashWithSalt(password: string, salt: string) {
  const hashRaw = await pbkdf2Promisified(password, salt)
  const hash = hashRaw.toString('hex')

  return { hash }
}

export async function generatePasswordHashWithoutSalt(password: string) {
  const saltBuffer = await randomBytes()
  const salt = saltBuffer.toString('hex')

  const hashRaw = await pbkdf2Promisified(password, salt)
  const hash = hashRaw.toString('hex')

  return { hash, salt }
}
