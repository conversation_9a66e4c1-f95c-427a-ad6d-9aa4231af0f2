import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

function formatApacheDate(date: Date) {
  const day = date.getDate().toString().padStart(2, '0')
  const month = months[date.getMonth()]
  const year = date.getFullYear()
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')

  const tzOffset = date.getTimezoneOffset()
  const tzSign = tzOffset <= 0 ? '+' : '-'
  const absTzOffset = Math.abs(tzOffset)
  const tzHours = Math.floor(absTzOffset / 60)
    .toString()
    .padStart(2, '0')
  const tzMinutes = (absTzOffset % 60).toString().padStart(2, '0')

  return `${day}/${month}/${year}:${hours}:${minutes}:${seconds} ${tzSign}${tzHours}${tzMinutes}`
}

export function middleware(request: NextRequest) {
  const url = request.nextUrl ? `${request.nextUrl.pathname}${request.nextUrl.search}` : request.url

  const remoteAddr =
    request.headers.get('x-real-ip') || request.headers.get('x-forwarded-for') || '-'

  const response = NextResponse.next()
  const clonedResponse = response.clone()

  const contentLength = clonedResponse.headers.get('content-length') || '-'
  const status = clonedResponse.status

  const logLine = [
    remoteAddr,
    '-',
    '-',
    `[${formatApacheDate(new Date())}]`,
    `"${request.method} ${url} HTTP/1.0"`,
    status,
    contentLength,
    `"${request.headers.get('referer') || '-'}"`,
    `"${request.headers.get('user-agent') || '-'}"`,
  ].join(' ')

  console.log(logLine)

  return response
}

export const config = {
  matcher: '/api/:path*',
}
