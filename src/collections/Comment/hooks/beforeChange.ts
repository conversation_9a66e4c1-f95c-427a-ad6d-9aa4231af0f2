import type { CollectionBeforeChangeHook } from 'payload'

// The `user` collection has access control locked so that users are not publicly accessible
// This means that we need to populate the authors manually here to protect user privacy
// GraphQL will not return mutated user data that differs from the underlying schema
// So we use an alternative `populatedAuthors` field to populate the user data, hidden from the admin UI
export const beforeChange: CollectionBeforeChangeHook = async ({ data, req, req: { payload } }) => {
  if (data?.userId && !data.commentUser) {
    const author = await payload.findByID({
      id: data.userId,
      collection: 'users',
      depth: 0,
      req,
    })
    if (author) {
      data.commentUser = author.name
    }
  }
  data.totalScore =
    (data.easeOfUse + data.navigation + data.contentQuality + data.cuisine + data.valueForMoney) / 5
  if (!data.commentDate) {
    data.commentDate = new Date()
  }
  console.log(data)
  return data
}
