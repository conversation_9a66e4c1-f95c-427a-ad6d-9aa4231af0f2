import type { CollectionConfig } from 'payload'

import { administrator } from '@/access/administrator'
import { authenticated } from '@/access/authenticated'
import { none } from '@/access/none'
import { owner } from '@/access/owner'
import { beforeChange } from './hooks/beforeChange'

export const Comment: CollectionConfig = {
  slug: 'comments',
  labels: {
    singular: {
      en: 'Comment',
      zh: '评论',
    },
    plural: {
      en: 'Comments',
      zh: '评论',
    },
  },
  access: {
    create: authenticated,
    delete: administrator,
    read: administrator,
    update: administrator,
  },
  fields: [
    {
      name: 'userId',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'itineraryId',
      type: 'relationship',
      relationTo: 'itineraries',
      required: true,
    },
    {
      name: 'easeOfUse',
      type: 'number',
      required: true,
      label: {
        en: 'Ease of Use',
        zh: '易用性',
      },
    },
    {
      name: 'navigation',
      type: 'number',
      required: true,
      label: {
        en: 'Navigation',
        zh: '导航',
      },
    },
    {
      name: 'contentQuality',
      type: 'number',
      required: true,
      label: {
        en: 'Content Quality',
        zh: '内容质量',
      },
    },
    {
      name: 'cuisine',
      type: 'number',
      required: true,
      label: {
        en: 'Cuisine',
        zh: '美食',
      },
    },
    {
      name: 'valueForMoney',
      type: 'number',
      required: true,
      label: {
        en: 'Value for Money',
        zh: '性价比',
      },
    },
    {
      name: 'totalScore',
      type: 'number',
      label: {
        en: 'Total Score',
        zh: '总分',
      },
    },
    {
      name: 'commentContent',
      type: 'text',
      required: true,
      label: {
        en: 'Comment Content',
        zh: '评价内容',
      },
    },
    {
      name: 'commentDate',
      type: 'date',
      label: {
        en: 'Comment Date',
        zh: '评价日期',
      },
    },
    {
      name: 'commentUser',
      type: 'text',
      label: {
        en: 'Comment User',
        zh: '用户名称',
      },
    },
    {
      name: 'isDisplayed',
      type: 'checkbox',
      label: {
        en: 'Is Displayed',
        zh: '是否展示',
      },
    },
    {
      name: 'isPinned',
      type: 'checkbox',
      label: {
        en: 'Is Pinned',
        zh: '是否置顶',
      },
    },
  ],
  hooks: {
    beforeChange: [beforeChange],
  },
}
