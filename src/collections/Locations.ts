import type { CollectionConfig } from 'payload'
import { defaultContentLexical } from '../fields/defaultLexical'
import { BusinessHoursField } from '../fields/businessHours'

const supportMenuTypes = ['restaurant', 'museum']

export const Locations: CollectionConfig = {
  slug: 'locations',
  admin: {
    useAsTitle: 'name',
  },
  labels: {
    singular: {
      en: 'Location',
      zh: '地点',
    },
    plural: {
      en: 'Locations',
      zh: '地点',
    },
  },
  fields: [
    {
      type: 'row',
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
          localized: true,
          label: {
            en: 'Name',
            zh: '名称',
          },
          admin: {
            width: '40%',
          },
        },
        {
          name: 'poiType',
          type: 'select',
          required: true,
          admin: {
            width: '40%',
          },
          label: {
            en: 'Type of Location',
            zh: '地点类型',
          },
          options: [
            {
              label: {
                en: 'Attraction',
                zh: '景点',
              },
              value: 'attraction',
            },
            {
              label: {
                en: 'Shop',
                zh: '商店',
              },
              value: 'shop',
            },
            {
              label: {
                en: 'Restaurant',
                zh: '餐厅',
              },
              value: 'restaurant',
            },
            {
              label: {
                en: 'Beverage',
                zh: '饮品',
              },
              value: 'beverage',
            },
            {
              label: {
                en: 'Dining',
                zh: '用餐',
              },
              value: 'dining',
            },
            {
              label: {
                en: 'Leisure',
                zh: '休闲',
              },
              value: 'leisure',
            },
            {
              label: {
                en: 'Accommodation',
                zh: '住宿',
              },
              value: 'accommodation',
            },
            {
              label: {
                en: 'Transportation',
                zh: '交通',
              },
              value: 'transportation',
            },
            {
              label: {
                en: ' Museum',
                zh: '博物馆',
              },
              value: 'museum',
            },
            {
              label: { en: 'other', zh: '其他' },
              value: 'other',
            },
          ],
        },
        {
          name: 'isPopular',
          type: 'checkbox',
          label: {
            en: 'Popular',
            zh: '热门',
          },
          defaultValue: false,
          admin: {
            width: '20%',
            style: { alignItems: 'center', justifyContent: 'center' },
          },
        },
      ],
    },
    {
      type: 'tabs',
      tabs: [
        {
          label: {
            en: 'Basic Information',
            zh: '基本信息',
          },
          fields: [
            {
              name: 'displayName',
              type: 'text',
              required: true,
              localized: true,
              label: {
                en: 'Display Name',
                zh: '显示名称',
              },
            },
            {
              name: 'chineseName',
              type: 'text',
              localized: true,
              label: {
                en: 'Chinese Name',
                zh: '中文名称',
              },
            },
            {
              name: 'city',
              type: 'relationship',
              relationTo: 'cities',
              required: true,
              label: {
                en: 'City',
                zh: '所属城市',
              },
            },
            {
              name: 'introduction',
              type: 'textarea',
              required: true,
              localized: true,
              label: {
                en: 'Introduction',
                zh: '简介',
              },
            },
            {
              name: 'address',
              type: 'text',
              required: true,
              localized: true,
              label: {
                en: 'Address',
                zh: '详细地址',
              },
            },
            {
              name: 'phone',
              type: 'text',
              required: false,
              localized: true,
              label: {
                en: 'Phone',
                zh: '联系电话',
              },
            },
            {
              name: 'location',
              type: 'point',
              required: true,
              label: {
                en: 'Location',
                zh: '地理位置',
              },
            },
            {
              name: 'heroImage',
              type: 'upload',
              relationTo: 'media',
              required: true,
              label: {
                en: 'Cover Image',
                zh: '封面图',
              },
            },
            {
              name: 'images',
              type: 'upload',
              relationTo: 'media',
              required: true,
              hasMany: true,
              label: {
                en: 'Images',
                zh: '相册图片',
              },
            },
            {
              name: 'audio',
              type: 'relationship',
              relationTo: 'audio',
              label: {
                en: 'Audio(deprecated)',
                zh: '音频介绍（弃用）',
              },
            },
            {
              name: 'audioLocalized',
              type: 'relationship',
              relationTo: 'audio',
              label: {
                en: 'Audio',
                zh: '音频介绍',
              },
              localized: true,
            },
            {
              name: 'dianpingLink',
              type: 'text',
              label: {
                en: 'dianpingLink',
                zh: '点评链接',
              },
            },
          ],
        },
        {
          label: {
            en: 'Tour Description',
            zh: '旅游介绍',
          },
          fields: [
            {
              name: 'description',
              type: 'richText',
              localized: true,
              editor: defaultContentLexical,
              label: false,
            },
          ],
        },
        {
          label: {
            en: 'Business Hours',
            zh: '营业时间',
          },
          fields: [
            {
              name: 'businessHours',
              type: 'relationship',
              relationTo: 'business-hours',
              label: {
                en: 'Business Hours',
                zh: '营业时间',
              },
            },
          ],
        },
        {
          label: {
            zh: '子地点',
            en: 'Sub Locations',
          },
          fields: [
            {
              name: 'locations',
              type: 'array',
              required: false,
              admin: {
                initCollapsed: false,
              },
              label: {
                en: 'Sub Locations',
                zh: '子地点',
              },
              fields: [
                {
                  name: 'location',
                  type: 'relationship',
                  relationTo: 'locations',
                  required: true,
                  label: {
                    en: 'Select Location',
                    zh: '选择地点',
                  },
                },
                {
                  name: 'timeRange',
                  type: 'group',
                  label: {
                    en: 'Time Range',
                    zh: '游玩时间',
                  },
                  admin: {
                    style: {
                      display: 'flex',
                      gap: '10px',
                    },
                  },
                  fields: [
                    {
                      type: 'row',
                      fields: [
                        {
                          name: 'startTime',
                          type: 'date',
                          admin: {
                            date: {
                              pickerAppearance: 'timeOnly',
                              displayFormat: 'HH:mm a',
                              timeFormat: 'HH:mm a',
                            },
                            width: '50%',
                          },
                          label: {
                            en: 'Start Time',
                            zh: '开始时间',
                          },
                        },
                        {
                          name: 'endTime',
                          type: 'date',
                          admin: {
                            date: {
                              pickerAppearance: 'timeOnly',
                              displayFormat: 'HH:mm a',
                              timeFormat: 'HH:mm a',
                            },
                            width: '50%',
                          },
                          label: {
                            en: 'End Time',
                            zh: '结束时间',
                          },
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          label: {
            en: 'Restaurant Menu',
            zh: '餐厅菜单',
          },
          fields: [
            {
              name: 'restaurantMenu',
              type: 'array',
              label: {
                en: 'Restaurant Menu',
                zh: '餐厅菜单',
              },
              admin: {
                condition: (data, subData) => {
                  return supportMenuTypes.includes(subData.poiType)
                },
                description: {
                  en: 'Required fields for restaurant type or museum type',
                  zh: '地点类型为餐厅或博物馆时需要填写',
                },
              },
              fields: [
                {
                  name: 'dishName',
                  type: 'text',
                  label: {
                    en: 'Dish Name',
                    zh: '菜名',
                  },
                  localized: true,
                  required: true,
                },
                {
                  name: 'cuisine',
                  type: 'select',
                  label: {
                    en: 'Cuisine Type',
                    zh: '菜系',
                  },
                  options: [
                    {
                      label: {
                        en: 'Chinese',
                        zh: '中餐',
                      },
                      value: 'chinese',
                    },
                    {
                      label: {
                        en: 'Western',
                        zh: '西餐',
                      },
                      value: 'western',
                    },
                    {
                      label: {
                        en: 'Japanese',
                        zh: '日本料理',
                      },
                      value: 'japanese',
                    },
                    {
                      label: {
                        en: 'Other',
                        zh: '其他',
                      },
                      value: 'other',
                    },
                  ],
                },
                {
                  name: 'audio',
                  type: 'relationship',
                  relationTo: 'audio',
                  label: {
                    en: 'Audio(deprecated)',
                    zh: '音频介绍（弃用）',
                  },
                },
                {
                  name: 'audioLocalized',
                  type: 'relationship',
                  relationTo: 'audio',
                  label: {
                    en: 'Audio',
                    zh: '音频介绍',
                  },
                  localized: true,
                },
                {
                  name: 'dishPhotos',
                  type: 'upload',
                  relationTo: 'media',
                  hasMany: true,
                  label: {
                    en: 'Dish Photos',
                    zh: '菜品相册',
                  },
                },
                {
                  name: 'review',
                  label: {
                    en: 'Dish Review',
                    zh: '菜品点评',
                  },
                  type: 'richText',
                  localized: true,
                  editor: defaultContentLexical,
                },
              ],
            },
          ],
        },
      ],
    },
  ],
}
