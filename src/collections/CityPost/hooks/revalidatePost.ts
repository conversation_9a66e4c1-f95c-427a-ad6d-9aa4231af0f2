import { revalidatePath, revalidateTag } from 'next/cache'
import type { CollectionAfter<PERSON>hangeHook, CollectionAfterDeleteHook } from 'payload'

import type { CityPost } from '../../../payload-types'

export const revalidatePost: CollectionAfterChangeHook<CityPost> = ({
  doc,
  previousDoc,
  req: { payload, context },
}) => {
  if (!context.disableRevalidate) {
    if (doc._status === 'published') {
      const path = `/city-posts/${doc.slug}`

      payload.logger.info(`Revalidating post at path: ${path}`)

      revalidatePath(path)
      revalidateTag('city-posts-sitemap')
    }

    // If the post was previously published, we need to revalidate the old path
    if (previousDoc._status === 'published' && doc._status !== 'published') {
      const oldPath = `/city-posts/${previousDoc.slug}`

      payload.logger.info(`Revalidating old post at path: ${oldPath}`)

      revalidatePath(oldPath)
      revalidateTag('city-posts-sitemap')
    }
  }
  return doc
}

export const revalidateDelete: CollectionAfterDeleteHook<CityPost> = ({
  doc,
  req: { context },
}) => {
  if (!context.disableRevalidate) {
    const path = `/city-posts/${doc?.slug}`

    revalidatePath(path)
    revalidateTag('city-posts-sitemap')
  }

  return doc
}
