import type { CollectionConfig } from 'payload'

import { administrator } from '@/access/administrator'
import { authenticated } from '@/access/authenticated'
import { none } from '@/access/none'
import { translations } from '@/languages/translations'

export const Voucher: CollectionConfig = {
  slug: 'vouchers',
  labels: {
    singular: {
      en: 'Voucher',
      zh: '优惠券',
    },
    plural: {
      en: 'Vouchers',
      zh: '优惠券',
    },
  },
  access: {
    create: authenticated,
    delete: none,
    read: authenticated,
    update: administrator,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      defaultValue: ({ locale, req }) =>
        translations[locale || req.i18n.language]?.general.defaultVoucherName,
    },
    {
      name: 'userId',
      type: 'relationship',
      relationTo: 'users',
    },
    {
      name: 'invitationCodeId',
      type: 'relationship',
      relationTo: 'invitation-codes',
    },
    {
      name: 'discount',
      type: 'number',
      required: true,
      min: 0,
      max: 1,
      defaultValue: 0.75,
    },
    {
      name: 'used',
      type: 'checkbox',
      required: true,
      defaultValue: false,
    },
    {
      name: 'expiresAt',
      type: 'date',
      required: true,
      defaultValue: () => new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000),
    },
  ],
}
