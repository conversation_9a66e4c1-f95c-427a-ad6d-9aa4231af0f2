import type { CollectionConfig } from 'payload'

import { authenticated } from '@/access/authenticated'
import { none } from '@/access/none'
import { owner } from '@/access/owner'

export const Orders: CollectionConfig = {
  slug: 'orders',
  labels: {
    singular: {
      en: 'Order',
      zh: '订单',
    },
    plural: {
      en: 'Orders',
      zh: '订单',
    },
  },
  access: {
    create: authenticated,
    delete: owner(),
    read: authenticated,
    update: none,
  },
  fields: [
    {
      name: 'userId',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'stripePaymentIntentID',
      type: 'text',
      admin: {
        position: 'sidebar',
      },
      required: true,
    },
    {
      name: 'itineraryId',
      type: 'relationship',
      relationTo: 'itineraries',
      hasMany: true,
      required: true,
    },
    {
      name: 'voucherId',
      type: 'relationship',
      relationTo: 'vouchers',
    },
    {
      name: 'amount',
      type: 'number',
      required: true,
    },
    {
      name: 'currency',
      type: 'text',
      required: true,
    },
  ],
}
