import type { CollectionConfig } from 'payload'

import { anyone } from '../access/anyone'
import { authenticated } from '../access/authenticated'

export const GuideCategories: CollectionConfig = {
  slug: 'guide-categories',
  defaultSort: 'order',
  labels: {
    singular: {
      en: 'Guide Category',
      zh: '指南分类',
    },
    plural: {
      en: 'Guide Categories',
      zh: '指南分类',
    },
  },
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'order'],
  },
  fields: [
    {
      type: 'row',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
          localized: true,
          label: {
            en: 'Title',
            zh: '标题',
          },
        },
      ],
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      required: true,
      label: {
        en: 'Logo',
        zh: 'Logo',
      },
    },
    {
      name: 'order',
      type: 'number',
      required: true,
      label: {
        en: 'Order',
        zh: '排序',
      },
      admin: {
        position: 'sidebar',
        description: {
          zh: '排序越小，越靠前',
          en: 'The smaller the order, the higher the priority',
        },
      },
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: {
        en: 'Slug',
        zh: 'Slug',
      },
      admin: {
        position: 'sidebar',
      },
    },
  ],
}
