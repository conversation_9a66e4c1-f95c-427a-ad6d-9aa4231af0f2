import type { CollectionConfig } from 'payload'

import { administrator } from '@/access/administrator'
import { authenticated } from '@/access/authenticated'
import { none } from '@/access/none'

export const RedemptionCodes: CollectionConfig = {
  slug: 'redemption-codes',
  labels: {
    singular: {
      en: 'Redemption Code',
      zh: '兑换码',
    },
    plural: {
      en: 'Redemption Codes',
      zh: '兑换码',
    },
  },
  access: {
    create: administrator,
    delete: none,
    read: authenticated,
    update: none,
  },
  fields: [
    {
      name: 'userId',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'numbers',
      type: 'text',
      required: true,
    },
    {
      name: 'redeemed',
      type: 'checkbox',
      defaultValue: false,
    },
  ],
}
