import type { CollectionConfig } from 'payload'

import { administrator } from '@/access/administrator'
import { authenticated } from '@/access/authenticated'
import { none } from '@/access/none'
import { generateRandomUUID } from '@/utilities/generateRandomCode'

export const InvitationCodes: CollectionConfig = {
  slug: 'invitation-codes',
  labels: {
    singular: {
      en: 'Invitation Code',
      zh: '邀请码',
    },
    plural: {
      en: 'Invitation Codes',
      zh: '邀请码',
    },
  },
  access: {
    create: authenticated,
    delete: none,
    read: authenticated,
    update: administrator,
  },
  fields: [
    {
      name: 'userId',
      type: 'relationship',
      relationTo: 'users',
    },
    {
      name: 'name',
      type: 'text',
    },
    {
      name: 'numbers',
      type: 'text',
      required: true,
      unique: true,
      defaultValue: () => generateRandomUUID(),
    },
    {
      name: 'currentUsage',
      type: 'number',
      required: true,
      defaultValue: 0,
    },
    {
      name: 'maxUsage',
      type: 'number',
      required: true,
      defaultValue: 5000,
      min: 1,
    },
    {
      name: 'discount',
      type: 'number',
      required: true,
      min: 0,
      max: 1,
      defaultValue: 0.75,
    },
    {
      name: 'expiresAt',
      type: 'date',
      required: true,
      defaultValue: () => new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000),
    },
  ],
}
