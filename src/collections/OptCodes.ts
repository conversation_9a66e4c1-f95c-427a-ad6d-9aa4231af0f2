import type { CollectionConfig } from 'payload'

import { authenticated } from '@/access/authenticated'
import { none } from '@/access/none'
import { CodeType } from '@/enums/code-type'

export const OtpCode: CollectionConfig = {
  slug: 'opt-codes',
  labels: {
    singular: {
      en: 'OTP Code',
      zh: 'OTP验证码',
    },
    plural: {
      en: 'OTP Codes',
      zh: 'OTP验证码',
    },
  },
  access: {
    create: none,
    delete: none,
    read: authenticated,
    update: none,
  },
  fields: [
    {
      name: 'email',
      type: 'text',
      required: true,
    },
    {
      name: 'code',
      type: 'text',
      required: true,
    },
    {
      name: 'type',
      type: 'text',
      required: true,
      validate: (value: string) => {
        if (value !== CodeType.FORGOT && value !== CodeType.REGISTER) {
          return 'Invalid code type'
        }
        return true
      },
    },
    {
      name: 'expiresAt',
      type: 'date',
      required: true,
    },
    {
      name: 'verified',
      type: 'checkbox',
      defaultValue: false,
    },
  ],
}
