import { CollectionConfig } from 'payload'

export const DownloadRecords: CollectionConfig = {
  slug: 'download-records',
  admin: {
    useAsTitle: 'email',
  },
  labels: {
    singular: {
      en: 'Download Record',
      zh: '下载记录',
    },
    plural: {
      en: 'Download Records',
      zh: '下载记录',
    },
  },
  fields: [
    {
      name: 'email',
      type: 'email',
      required: true,
    },
    {
      name: 'formId',
      type: 'text',
      required: true,
    },
    {
      name: 'submissionId',
      type: 'text',
      required: true,
    },
    {
      name: 'downloadTime',
      type: 'date',
      required: true,
    },
    {
      name: 'ipAddress',
      type: 'text',
    },
  ],
}
