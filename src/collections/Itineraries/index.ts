import type { CollectionConfig } from 'payload'

import { defaultContentLexical } from '@/fields/defaultLexical'
import { addToCart } from './endpoints/addToCart'
import { loadComments } from './endpoints/loadComments'

export const Itineraries: CollectionConfig = {
  slug: 'itineraries',
  labels: {
    singular: {
      en: 'Itinerary',
      zh: '行程',
    },
    plural: {
      en: 'Itineraries',
      zh: '行程',
    },
  },
  admin: {
    defaultColumns: ['title', 'theme', 'city'],
    useAsTitle: 'title',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      localized: true,
      label: {
        en: 'Itinerary Title',
        zh: '行程标题',
      },
    },
    {
      name: 'theme',
      type: 'textarea',
      required: true,
      localized: true,
      label: {
        en: 'Itinerary Theme',
        zh: '行程主题',
      },
    },
    {
      name: 'productId',
      type: 'relationship',
      relationTo: 'products',
      // required: true,
      label: {
        en: 'Product ID',
        zh: '产品ID',
      },
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'city',
      type: 'relationship',
      relationTo: 'cities',
      required: true,
      label: {
        en: 'Destination City',
        zh: '目的地城市',
      },
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'tags',
      type: 'relationship',
      relationTo: 'itineraries-tags',
      hasMany: true,
      required: true,
      label: {
        en: 'Itineraries Tags',
        zh: '行程标签',
      },
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'overview',
      type: 'textarea',
      required: true,
      localized: true,
      label: {
        en: 'Itinerary Overview',
        zh: '行程总体介绍',
      },
    },
    {
      label: {
        en: 'Precautions',
        zh: '注意事项',
      },
      name: 'precautions',
      type: 'textarea',
      localized: true,
    },
    {
      label: {
        en: 'Precautions(richText)',
        zh: '注意事项（富文本）',
      },
      name: 'precautionsRichText',
      type: 'richText',
      localized: true,
      editor: defaultContentLexical,
    },
    {
      name: 'heroImage',
      type: 'upload',
      relationTo: 'media',
      required: true,
      label: {
        en: 'Hero Image',
        zh: '封面图',
      },
    },
    {
      name: 'albumImages',
      type: 'upload',
      relationTo: 'media',
      required: true,
      hasMany: true,
      label: {
        en: 'Album Images',
        zh: '相册图片',
      },
    },
    {
      name: 'manualPrice',
      type: 'number',
      label: {
        en: 'Manual Price',
        zh: '定价',
      },
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'rating',
      type: 'number',
      max: 5,
      min: 0,
      defaultValue: 5,
      label: {
        en: 'rating',
        zh: '评分',
      },
      admin: {
        position: 'sidebar',
      },
    },
    {
      type: 'row',
      admin: {
        position: 'sidebar',
      },
      fields: [
        {
          name: 'isPopular',
          type: 'checkbox',
          label: {
            en: 'Popular',
            zh: '是否热门',
          },
          defaultValue: false,
        },
        {
          name: 'isPublished',
          type: 'checkbox',
          label: {
            en: 'Published',
            zh: '是否发布',
          },
          defaultValue: false,
        },
      ],
    },
    {
      name: 'locations',
      type: 'array',
      required: true,
      admin: {
        initCollapsed: false,
      },
      label: {
        en: 'Itinerary Locations',
        zh: '行程地点',
      },
      fields: [
        {
          name: 'location',
          type: 'relationship',
          relationTo: 'locations',
          required: true,
          label: {
            en: 'Select Location',
            zh: '选择地点',
          },
        },
        {
          name: 'timeRange',
          type: 'group',
          label: {
            en: 'Time Range',
            zh: '游玩时间',
          },
          admin: {
            style: {
              display: 'flex',
              gap: '10px',
            },
          },
          fields: [
            {
              type: 'row',
              fields: [
                {
                  name: 'startTime',
                  type: 'date',
                  required: true,
                  admin: {
                    date: {
                      pickerAppearance: 'timeOnly',
                      displayFormat: 'HH:mm a',
                      timeFormat: 'HH:mm a',
                    },
                    width: '50%',
                  },
                  label: {
                    en: 'Start Time',
                    zh: '开始时间',
                  },
                },
                {
                  name: 'endTime',
                  type: 'date',
                  required: true,
                  admin: {
                    date: {
                      pickerAppearance: 'timeOnly',
                      displayFormat: 'HH:mm a',
                      timeFormat: 'HH:mm a',
                    },
                    width: '50%',
                  },
                  label: {
                    en: 'End Time',
                    zh: '结束时间',
                  },
                },
              ],
            },
          ],
        },
      ],
    },
    // 详情页重构
    {
      name: 'relatedItineraries',
      type: 'relationship',
      relationTo: 'itineraries',
      hasMany: true,
      required: true,
      label: {
        en: 'Related Itineraries',
        zh: '相关行程',
      },
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'availableTime',
      type: 'text',
      required: true,
      localized: true,
      label: {
        en: 'Available Time',
        zh: '可用时间',
      },
    },
    {
      name: 'estimatedDuration',
      type: 'text',
      required: true,
      localized: true,
      label: {
        en: 'Estimated Duration',
        zh: '预计时长',
      },
    },
    {
      name: 'validityPeriod',
      type: 'number',
      required: true,
      label: {
        en: 'Validity Period(year)',
        zh: '有效期(年)',
      },
    },
    {
      name: 'instantPurchase',
      type: 'text',
      required: true,
      localized: true,
      label: {
        en: 'Instant Purchase',
        zh: '立即购买',
      },
    },
    {
      name: 'numberOfDishes',
      type: 'number',
      required: true,
      label: {
        en: 'Number of Dishes',
        zh: '美食数量',
      },
    },
    {
      name: 'whatIncludes',
      type: 'text',
      required: true,
      localized: true,
      label: {
        en: 'What Includes',
        zh: '包含内容',
      },
    },
    {
      name: 'whatNotIncludes',
      type: 'text',
      required: true,
      localized: true,
      label: {
        en: 'What Not Includes',
        zh: '不包含内容',
      },
    },
    // {
    //   name: 'relatedPosts',
    //   type: 'array',
    //   label: {
    //     en: 'Related Blog Posts',
    //     zh: '相关博客文章',
    //   },
    //   fields: [
    //     {
    //       name: 'post',
    //       type: 'relationship',
    //       relationTo: 'posts',
    //       required: true,
    //       label: {
    //         en: 'Select Post',
    //         zh: '选择文章',
    //       },
    //       admin: {
    //         description: {
    //           en: 'Choose a blog post related to this itinerary',
    //           zh: '选择与此行程相关的博客文章',
    //         },
    //       },
    //     },
    //     {
    //       name: 'description',
    //       type: 'textarea',
    //       label: {
    //         en: 'Relationship Description',
    //         zh: '关联说明',
    //       },
    //       admin: {
    //         description: {
    //           en: 'Explain how this post is related to the itinerary',
    //           zh: '解释这篇文章与行程的关联',
    //         },
    //       },
    //     },
    //   ],
    // },
  ],
  endpoints: [
    {
      path: '/:id/add-to-cart',
      method: 'post',
      handler: addToCart,
    },
    {
      path: '/:id/load-comments',
      method: 'get',
      handler: loadComments,
    },
  ],
}
