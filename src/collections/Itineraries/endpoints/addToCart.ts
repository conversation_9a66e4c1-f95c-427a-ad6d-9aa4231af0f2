import { getNextRequestI18n } from '@payloadcms/next/utilities'
import type { PayloadRequest } from 'payload'

import type { TranslationsKeys, TranslationsObject } from '@/languages/translations'
import { Cart } from '@/payload-types'

export async function addToCart(req: PayloadRequest): Promise<Response> {
  const { routeParams, payload, user } = req

  const i18n = await getNextRequestI18n<TranslationsObject, TranslationsKeys>({
    config: payload.config,
  })

  if (!routeParams || !routeParams.id) {
    return Response.json(
      { error: i18n.t('error:invalidRequestArgs', { args: 'id' }) },
      { status: 400 },
    )
  }
  if (!user) {
    return Response.json({ error: i18n.t('error:unauthorized') }, { status: 401 })
  }

  const { totalDocs } = await payload.db.count({
    collection: 'itineraries',
    where: { id: { equals: routeParams.id } },
  })
  if (totalDocs === 0) {
    return Response.json(
      { error: i18n.t('error:notFound', { resource: i18n.t('collection:itinerary') }) },
      { status: 404 },
    )
  }

  const cart = await payload.db.findOne<Cart>({
    collection: 'carts',
    where: { userId: { equals: user.id } },
  })
  if (!cart) {
    return Response.json(
      { error: i18n.t('error:notFound', { resource: i18n.t('collection:cart') }) },
      { status: 404 },
    )
  }

  const paramId = routeParams.id.toString()
  const currentItineraryIds = cart.items!.map((item) => (typeof item === 'string' ? item : item.id))
  if (currentItineraryIds.includes(paramId)) {
    return Response.json({ error: i18n.t('error:alreadyInCart') }, { status: 400 })
  }
  const orderCount = await payload.db.count({
    collection: 'user-order-itineraries',
    where: { userId: { equals: user.id }, itineraryId: { equals: paramId } },
  })
  if (orderCount.totalDocs > 0) {
    return Response.json({ error: i18n.t('error:alreadyPurchasedOrRedeemed') }, { status: 400 })
  }

  await payload.db.updateOne({
    collection: 'carts',
    id: cart.id,
    data: {
      items: [...currentItineraryIds, routeParams.id],
    },
  })

  return Response.json({ message: i18n.t('general:addToCartSuccessfully') }, { status: 200 })
}
