import type { CollectionConfig } from 'payload'
import { BusinessHoursField } from '../fields/businessHours'

export const Restaurants: CollectionConfig = {
  slug: 'restaurants',
  labels: {
    singular: {
      en: 'Restaurant',
      zh: '餐厅',
    },
    plural: {
      en: 'Restaurants',
      zh: '餐厅',
    },
  },
  admin: {
    useAsTitle: 'name',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: {
        en: 'Restaurant Name',
        zh: '餐厅名称',
      },
    },
    {
      name: 'cuisine',
      type: 'select',
      options: [
        {
          label: {
            en: 'Chinese',
            zh: '中餐',
          },
          value: 'chinese',
        },
        {
          label: {
            en: 'Western',
            zh: '西餐',
          },
          value: 'western',
        },
        {
          label: {
            en: 'Japanese',
            zh: '日本料理',
          },
          value: 'japanese',
        },
        {
          label: {
            en: 'Other',
            zh: '其他',
          },
          value: 'other',
        },
      ],
      label: {
        en: 'Cuisine Type',
        zh: '菜系',
      },
    },
    BusinessHoursField,
    {
      name: 'address',
      type: 'text',
      label: {
        en: 'Address',
        zh: '地址',
      },
    },
    {
      name: 'contactNumber',
      type: 'text',
      label: {
        en: 'Contact Number',
        zh: '联系电话',
      },
    },
    {
      name: 'coverImage',
      type: 'upload',
      relationTo: 'media',
      label: {
        en: 'Cover Image',
        zh: '封面图',
      },
    },
  ],
}
