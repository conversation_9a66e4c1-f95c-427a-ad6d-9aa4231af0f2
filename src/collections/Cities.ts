import type { CollectionConfig } from 'payload'

import { anyone } from '../access/anyone'
import { authenticated } from '../access/authenticated'

export const Cities: CollectionConfig = {
  slug: 'cities',
  labels: {
    singular: {
      en: 'City',
      zh: '城市',
    },
    plural: {
      en: 'Cities',
      zh: '城市',
    },
  },
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
  },
  fields: [
    {
      name: 'order',
      type: 'number',
      label: {
        en: 'Order',
        zh: '排序',
      },
      admin: {
        position: 'sidebar',
        description: {
          en: 'Lower numbers appear first',
          zh: '数字越小，排序越靠前',
        },
      },
      required: true,
      index: true,
      defaultValue: 0,
    },
    {
      name: 'title',
      type: 'text',
      required: true,
      unique: true,
      localized: true,
      label: {
        en: 'City Name',
        zh: '城市名称',
      },
    },
    {
      name: 'location',
      type: 'point',
      required: true,
      label: {
        en: 'Location',
        zh: '地理位置',
      },
    },
    {
      name: 'heroImage',
      type: 'upload',
      relationTo: 'media',
      required: true,
      label: {
        en: 'Cover Image',
        zh: '封面图',
      },
    },
    {
      name: 'introduction',
      type: 'textarea',
      localized: true,
      label: {
        en: 'City Introduction',
        zh: '城市介绍',
      },
      admin: {
        description: {
          en: 'Brief description of the city',
          zh: '城市简介',
        },
      },
    },
    {
      name: 'guidePost',
      type: 'relationship',
      relationTo: 'city-posts',
      localized: true,
      label: {
        en: 'City Guide Post',
        zh: '城市指南文章',
      },
      admin: {
        description: {
          en: 'Guide post of the city',
          zh: '城市指南文章',
        },
      },
    },
    {
      name: 'albumImages',
      type: 'array',
      label: {
        en: 'Album Images',
        zh: '相册图片',
      },
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'caption',
          type: 'textarea',
          required: true,
          localized: true,
          label: {
            en: 'Image Caption',
            zh: '图片说明',
          },
        },
      ],
    },
    {
      name: 'bestTimeToVisit',
      type: 'text',
      localized: true,
      label: {
        en: 'Best Time to Visit',
        zh: '最佳旅游时间',
      },
    },
    {
      name: 'climate',
      type: 'text',
      localized: true,
      label: {
        en: 'Climate',
        zh: '气候',
      },
    },
    {
      name: 'tags',
      type: 'array',
      label: {
        en: 'City Tags',
        zh: '城市标签',
      },
      fields: [
        {
          name: 'tag',
          type: 'text',
          localized: true,
          label: {
            en: 'Tag',
            zh: '标签',
          },
        },
      ],
    },
  ],
}
