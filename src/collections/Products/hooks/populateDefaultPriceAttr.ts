import type { CollectionAfterReadHook } from 'payload'

import { Price } from '@/payload-types'

export const populateDefaultPriceAttr: CollectionAfterReadHook = async ({ doc, req }) => {
  const price = await req.payload.db.findOne<Price>({
    collection: 'prices',
    where: { stripeID: { equals: doc.defaultPrice } },
    select: { currency: true, unitAmount: true },
  })

  const defaultPriceCurrency = price?.currency ?? 'hkd'
  const unitAmount = price?.unitAmount ?? 0
  const defaultPriceUnitAmount = unitAmount / 100

  return { ...doc, defaultPriceCurrency, defaultPriceUnitAmount, unitAmount }
}
