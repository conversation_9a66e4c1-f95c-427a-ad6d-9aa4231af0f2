import type { CollectionConfig } from 'payload'

import { authenticated } from '@/access/authenticated'
import { none } from '@/access/none'
import { populateDefaultPriceAttr } from './hooks/populateDefaultPriceAttr'

export const Products: CollectionConfig = {
  slug: 'products',
  labels: {
    singular: {
      en: 'Product',
      zh: '产品',
    },
    plural: {
      en: 'Products',
      zh: '产品',
    },
  },
  access: {
    create: none,
    delete: none,
    read: authenticated,
    update: none,
  },
  admin: {
    useAsTitle: 'name',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
    },
    {
      name: 'defaultPrice',
      type: 'text',
      required: true,
    },
    {
      name: 'active',
      type: 'checkbox',
      required: true,
    },
  ],
  hooks: {
    afterRead: [populateDefaultPriceAttr],
  },
}
