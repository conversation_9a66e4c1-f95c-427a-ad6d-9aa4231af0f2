import type { CollectionConfig } from 'payload'

import { authenticated } from '@/access/authenticated'
import { owner } from '@/access/owner'

export const Carts: CollectionConfig = {
  slug: 'carts',
  labels: {
    singular: {
      en: 'Cart',
      zh: '购物车',
    },
    plural: {
      en: 'Carts',
      zh: '购物车',
    },
  },
  access: {
    create: authenticated,
    delete: owner(),
    read: authenticated,
    update: owner(),
  },
  fields: [
    {
      name: 'userId',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      unique: true,
    },
    {
      name: 'items',
      type: 'relationship',
      relationTo: 'itineraries',
      hasMany: true,
      defaultValue: [],
    },
  ],
}
