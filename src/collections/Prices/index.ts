import type { CollectionConfig } from 'payload'

import { authenticated } from '@/access/authenticated'
import { none } from '@/access/none'

export const Prices: CollectionConfig = {
  slug: 'prices',
  labels: {
    singular: {
      en: 'Price',
      zh: '价格',
    },
    plural: {
      en: 'Prices',
      zh: '价格',
    },
  },
  access: {
    create: none,
    delete: none,
    read: authenticated,
    update: none,
  },
  fields: [
    {
      name: 'product',
      type: 'text',
      required: true,
    },
    {
      name: 'currency',
      type: 'text',
      required: true,
    },
    {
      name: 'type',
      type: 'text',
      required: true,
    },
    {
      name: 'unitAmount',
      type: 'number',
    },
    {
      name: 'active',
      type: 'checkbox',
      required: true,
    },
  ],
}
