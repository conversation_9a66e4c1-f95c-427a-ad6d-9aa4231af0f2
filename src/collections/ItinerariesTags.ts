import type { CollectionConfig } from 'payload'

import { anyone } from '../access/anyone'
import { authenticated } from '../access/authenticated'

export const ItinerariesTags: CollectionConfig = {
  slug: 'itineraries-tags',
  defaultSort: 'order',
  labels: {
    singular: {
      en: 'Itineraries Tag',
      zh: '行程标签',
    },
    plural: {
      en: 'Itineraries Tags',
      zh: '行程标签',
    },
  },
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'order'],
  },
  fields: [
    {
      type: 'row',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
          label: {
            en: 'Title',
            zh: '标题',
          },
        },
        {
          name: 'order',
          type: 'number',
          required: true,
          label: {
            en: 'Order',
            zh: '排序',
          },
          admin: {
            description: {
              zh: '排序越小，越靠前',
              en: 'The smaller the order, the higher the priority',
            },
          },
        },
      ],
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      label: {
        en: 'Logo',
        zh: 'Logo',
      },
    },
  ],
}
