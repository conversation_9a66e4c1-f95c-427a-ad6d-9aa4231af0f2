import type { CollectionConfig } from 'payload'

import { authenticated } from '@/access/authenticated'
import { owner } from '@/access/owner'

export const UserOrderItineraries: CollectionConfig = {
  slug: 'user-order-itineraries',
  labels: {
    singular: {
      en: 'UserOrderItineraries',
      zh: '用户已购行程',
    },
    plural: {
      en: 'UserOrderItineraries',
      zh: '用户已购行程',
    },
  },
  access: {
    create: authenticated,
    delete: owner(),
    read: authenticated,
    update: owner(),
  },
  fields: [
    {
      name: 'userId',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'itineraryId',
      type: 'relationship',
      relationTo: 'itineraries',
      required: true,
    },
    {
      name: 'orderId',
      type: 'relationship',
      relationTo: 'orders',
      required: true,
    },
    {
      name: 'isCompleted',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'isSticky',
      type: 'checkbox',
      defaultValue: false,
    },
  ],
}
