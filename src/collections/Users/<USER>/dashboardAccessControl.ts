import type { CollectionBeforeLoginHook } from 'payload'
import { AuthenticationError, Forbidden } from 'payload'

import { User } from '@/payload-types'

const ALLOWED_ROLES = ['admin']

export const dashboardAccessControl: CollectionBeforeLoginHook<User> = async ({ req, user }) => {
  const { context, t } = req

  if (!user) {
    throw new AuthenticationError(t)
  }

  if (
    !context.ignoreDashboardAccessControl &&
    !(user.roles || []).some((role) => ALLOWED_ROLES.includes(role))
  ) {
    throw new Forbidden(t)
  }

  return user
}
