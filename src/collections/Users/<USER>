import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'
import { loginByEmail } from './endpoints/loginByEmail'
import { dashboardAccessControl } from './hooks/dashboardAccessControl'

export const Users: CollectionConfig = {
  slug: 'users',
  labels: {
    singular: {
      en: 'User',
      zh: '用户',
    },
    plural: {
      en: 'Users',
      zh: '用户',
    },
  },
  access: {
    admin: authenticated,
    create: authenticated,
    delete: authenticated,
    read: authenticated,
    update: authenticated,
  },
  admin: {
    defaultColumns: ['name', 'email'],
    useAsTitle: 'name',
  },
  auth: {
    maxLoginAttempts: 5,
    lockTime: 10 * 60 * 1000, // 10 minutes
    tokenExpiration: 60 * 60 * 24 * 14, // 14 days
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'avatar',
      type: 'relationship',
      relationTo: 'media',
    },
    {
      name: 'gender',
      type: 'select',
      options: [
        { label: 'male', value: 'male' },
        { label: 'female', value: 'female' },
      ],
    },
    {
      name: 'contry',
      type: 'text',
    },
    {
      name: 'roles',
      type: 'select',
      options: [
        { label: 'customer', value: 'customer' },
        { label: 'administrator', value: 'admin' },
      ],
      hasMany: true,
      defaultValue: ['customer'],
    },
    {
      name: 'invitationCodeId',
      type: 'relationship',
      relationTo: 'invitation-codes',
    },
  ],
  timestamps: true,
  endpoints: [
    {
      path: '/login-by-email',
      method: 'post',
      handler: loginByEmail,
    },
  ],
  hooks: {
    beforeLogin: [dashboardAccessControl],
  },
}
