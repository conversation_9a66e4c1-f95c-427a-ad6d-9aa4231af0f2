import type { PayloadRequest } from 'payload'

export async function loginByEmail(req: PayloadRequest): Promise<Response> {
  try {
    const data = await req.json!()

    const loginBody = await req.payload.login({
      collection: 'users',
      context: { ignoreDashboardAccessControl: true },
      data,
    })

    return Response.json(loginBody, { status: 200 })
  } catch (error) {
    return Response.json({ error: error.message || 'Server Error' }, { status: 500 })
  }
}
