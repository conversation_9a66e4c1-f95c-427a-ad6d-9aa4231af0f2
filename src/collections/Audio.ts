import type { CollectionConfig } from 'payload'
import { anyone } from '../access/anyone'
import { authenticated } from '../access/authenticated'

export const Audio: CollectionConfig = {
  slug: 'audio',
  labels: {
    singular: {
      en: 'Audio',
      zh: '音频',
    },
    plural: {
      en: 'Audio',
      zh: '音频',
    },
  },
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: {
        en: 'Title',
        zh: '标题',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: {
        en: 'Description',
        zh: '描述',
      },
    },
    {
      name: 'duration',
      type: 'number',
      label: {
        en: 'Duration (seconds)',
        zh: '时长（秒）',
      },
    },
  ],
  upload: {
    staticDir: 'audio',
    disableLocalStorage: true,
    mimeTypes: ['audio/*'],
  },
}
