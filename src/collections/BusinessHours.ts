import type { CollectionConfig } from 'payload'

import { anyone } from '../access/anyone'
import { authenticated } from '../access/authenticated'
import { BusinessHoursField } from '../fields/businessHours'

export const BusinessHours: CollectionConfig = {
  slug: 'business-hours',
  labels: {
    singular: {
      en: 'Business Hours',
      zh: '营业时间',
    },
    plural: {
      en: 'Business Hours',
      zh: '营业时间',
    },
  },
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: {
        en: 'Title',
        zh: '标题',
      },
    },
    BusinessHoursField,
  ],
}
