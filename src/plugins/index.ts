import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { formBuilderPlugin } from '@payloadcms/plugin-form-builder'
import { nestedDocsPlugin } from '@payloadcms/plugin-nested-docs'
import { redirectsPlugin } from '@payloadcms/plugin-redirects'
import { seoPlugin } from '@payloadcms/plugin-seo'
import { searchPlugin } from '@payloadcms/plugin-search'
import { Plugin } from 'payload'
import { revalidateRedirects } from '@/hooks/revalidateRedirects'
import { GenerateTitle, GenerateURL } from '@payloadcms/plugin-seo/types'
import { FixedToolbarFeature, HeadingFeature, lexicalEditor } from '@payloadcms/richtext-lexical'
import { stripePlugin } from '@payloadcms/plugin-stripe'

import { searchFields } from '@/search/fieldOverrides'
import { beforeSyncWithSearch } from '@/search/beforeSync'
import { paymentIntentSucceeded } from '@/stripe/webhook/paymentIntentSucceeded'
import { generateDownloadLink } from '@/utilities/generateDownloadLink'

import { Page, Post } from '@/payload-types'
import { getServerSideURL } from '@/utilities/getURL'
import { verifyToken } from '@/utilities/jwt'
import { openapi, scalar } from 'payload-oapi'

const generateTitle: GenerateTitle<Post | Page> = ({ doc }) => {
  return doc?.title ? `${doc.title} | EasyGo China` : 'EasyGo China'
}

const generateURL: GenerateURL<Post | Page> = ({ doc }) => {
  const url = getServerSideURL()

  return doc?.slug ? `${url}/${doc.slug}` : url
}

export const plugins: Plugin[] = [
  redirectsPlugin({
    collections: ['pages', 'posts'],
    overrides: {
      // @ts-expect-error - This is a valid override, mapped fields don't resolve to the same type
      fields: ({ defaultFields }) => {
        return defaultFields.map((field) => {
          if ('name' in field && field.name === 'from') {
            return {
              ...field,
              admin: {
                description: 'You will need to rebuild the website when changing this field.',
              },
            }
          }
          return field
        })
      },
      hooks: {
        afterChange: [revalidateRedirects],
      },
    },
  }),
  nestedDocsPlugin({
    collections: ['categories'],
  }),
  seoPlugin({
    generateTitle,
    generateURL,
  }),
  formBuilderPlugin({
    formOverrides: {
      fields: ({ defaultFields }) => {
        return defaultFields.map((field) => {
          if ('name' in field && field.name === 'confirmationMessage') {
            return {
              ...field,
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    FixedToolbarFeature(),
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                  ]
                },
              }),
            }
          }
          return field
        })
      },
    },
    formSubmissionOverrides: {
      slug: 'form-submissions',
      labels: {
        singular: {
          en: 'Form Submission',
          zh: '表单提交',
        },
        plural: {
          en: 'Form Submissions',
          zh: '表单提交',
        },
      },
      fields: ({ defaultFields }) => {
        return [
          ...defaultFields,
          {
            name: 'isValidEmail',
            type: 'checkbox',
            access: {
              update: ({ req, id }) => {
                // 如果是管理员，允许更新
                if (req.user) return true
                // 从请求头获取 token
                const token = req.headers.get('authorization')?.split(' ')[1]
                if (!token) return false

                try {
                  // 验证 token 并检查 submissionId 是否匹配
                  const { submissionId } = verifyToken(token)
                  return submissionId === id
                } catch {
                  return false
                }
              },
            },
          },
        ]
      },
    },
    beforeEmail: (emailsToSend, beforeChangeParams) => {
      const { data } = beforeChangeParams
      const ICE_AND_FIRE_FORM_ID = process.env.NEXT_PUBLIC_ICE_AND_FIRE_FORM_ID
      const HOUFU_PARADE_FORM_ID = process.env.NEXT_PUBLIC_HOUFU_PARADE_FORM_ID
      const formIds = [ICE_AND_FIRE_FORM_ID, HOUFU_PARADE_FORM_ID]
      if (formIds.includes(data.form)) {
        const titleSuffix =
          data.form === ICE_AND_FIRE_FORM_ID ? 'Harbin Self-Guided Tour' : 'Fuzhou Houfu Parade'
        const emailField = data.submissionData.find((field) => field.field === 'email')
        if (emailField?.value) {
          const downloadLink = generateDownloadLink(emailField.value, data.form, emailField.id)

          return emailsToSend.map((email) => ({
            ...email,
            html: `
              <h1>Thank you for your interest in our ${titleSuffix}!</h1>
              <p>Dear traveler,</p>
              <p>We're excited to share our carefully crafted Self-Guided Tour with you. You can download it using the link below:</p>
              <p><a href="${downloadLink}">Download Your Tour</a></p>
              <p>Here are some quick tips to get started:</p>
              <ul>
                <li>Review the full itinerary before your trip</li>
                <li>Book your tickets in advance as suggested</li>
                <li>Check the weather forecast for your planned day</li>
              </ul>
              <p>If you have any questions, feel free to contact <NAME_EMAIL></p>
              <p>Enjoy your journey!</p>
              <p>Best regards,<br>EasyGo China Team</p>
            `,
          }))
        }
      }
      return emailsToSend
    },
  }),
  searchPlugin({
    collections: ['posts'],
    beforeSync: beforeSyncWithSearch,
    searchOverrides: {
      fields: ({ defaultFields }) => {
        return [...defaultFields, ...searchFields]
      },
    },
  }),
  payloadCloudPlugin(),
  stripePlugin({
    isTestKey: Boolean(process.env.PAYLOAD_PUBLIC_STRIPE_IS_TEST_KEY),
    logs: true,
    rest: false,
    stripeSecretKey: process.env.STRIPE_SECRET_KEY || '',
    stripeWebhooksEndpointSecret: process.env.STRIPE_WEBHOOKS_SIGNING_SECRET,
    sync: [
      {
        collection: 'products',
        stripeResourceType: 'products',
        stripeResourceTypeSingular: 'product',
        fields: [
          { fieldPath: 'name', stripeProperty: 'name' },
          { fieldPath: 'description', stripeProperty: 'description' },
          { fieldPath: 'defaultPrice', stripeProperty: 'default_price' },
          { fieldPath: 'active', stripeProperty: 'active' },
        ],
      },
      {
        collection: 'prices',
        // @ts-ignore
        stripeResourceType: 'prices',
        // @ts-ignore
        stripeResourceTypeSingular: 'price',
        fields: [
          { fieldPath: 'product', stripeProperty: 'product' },
          { fieldPath: 'currency', stripeProperty: 'currency' },
          { fieldPath: 'type', stripeProperty: 'type' },
          { fieldPath: 'unitAmount', stripeProperty: 'unit_amount' },
          { fieldPath: 'active', stripeProperty: 'active' },
        ],
      },
    ],
    webhooks: {
      'payment_intent.succeeded': paymentIntentSucceeded,
    },
  }),
  openapi({ openapiVersion: '3.0', metadata: { title: 'Easygo China API', version: '0.0.1' } }),
  scalar({}),
]
