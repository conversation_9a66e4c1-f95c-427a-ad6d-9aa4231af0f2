import { useState, useEffect, useRef } from 'react'
import { Search } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import useAMapLoader from '@/hooks/useAMapLoader'

interface SearchLocationProps {
  placeholder: string
  onSelect: (location: { name: string; location: [number, number] }) => void
  value?: string
}

export default function SearchLocation({ placeholder, onSelect, value }: SearchLocationProps) {
  const [keyword, setKeyword] = useState('')
  const [suggestions, setSuggestions] = useState<any[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const autoComplete = useRef<any>(null)
  const placeSearch = useRef<any>(null)
  const isMapLoaded = useAMapLoader()

  useEffect(() => {
    if (isMapLoaded && !autoComplete.current) {
      // 使用 Autocomplete 而不是 AutoComplete
      autoComplete.current = new window.AMap.Autocomplete({
        city: '北京',
        citylimit: false,
      })

      placeSearch.current = new window.AMap.PlaceSearch({
        city: '北京',
        citylimit: false,
      })
    }
  }, [isMapLoaded])

  const handleSearch = (value: string) => {
    setKeyword(value)
    if (!value || !isMapLoaded || !autoComplete.current) {
      setSuggestions([])
      return
    }

    autoComplete.current.search(value, (status: string, result: any) => {
      if (status === 'complete') {
        setSuggestions(result.tips)
        setShowSuggestions(true)
      }
    })
  }

  const handleSelect = (item: any) => {
    if (!item.location) {
      // 如果没有直接的位置信息，使用 PlaceSearch 获取详细信息
      placeSearch.current.search(item.name, (status: string, result: any) => {
        if (status === 'complete' && result.poiList.pois.length > 0) {
          const poi = result.poiList.pois[0]
          onSelect({
            name: item.name,
            location: [poi.location.lng, poi.location.lat],
          })
        }
      })
    } else {
      onSelect({
        name: item.name,
        location: [item.location.lng, item.location.lat],
      })
    }

    setShowSuggestions(false)
    setKeyword(item.name)
  }

  return (
    <div className="relative">
      <div className="relative">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder={placeholder}
          value={value || keyword}
          onChange={(e) => handleSearch(e.target.value)}
          onFocus={() => setShowSuggestions(true)}
          className="pl-8"
        />
      </div>

      {showSuggestions && suggestions.length > 0 && (
        <Card className="absolute w-full mt-1 z-50 max-h-60 overflow-auto">
          <ul className="py-2">
            {suggestions.map((item, index) => (
              <li
                key={index}
                className="px-4 py-2 hover:bg-accent cursor-pointer"
                onClick={() => handleSelect(item)}
              >
                <div className="font-medium">{item.name}</div>
                <div className="text-sm text-muted-foreground">{item.district}</div>
              </li>
            ))}
          </ul>
        </Card>
      )}
    </div>
  )
}
