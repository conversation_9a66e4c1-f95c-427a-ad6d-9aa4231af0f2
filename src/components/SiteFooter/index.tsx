'use client'

import * as React from 'react'
import Link from 'next/link'
import { useState } from 'react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { ThemeSelector } from '@/providers/Theme/ThemeSelector'
import { FOOTER_LINKS, SOCIAL_LINKS } from './marketing-links'
import { Logo } from '@/components/Logo/Logo'
import { translations } from '@/languages/translations'

const APP_NAME = 'EasyGo China'

export function Footer(): React.JSX.Element {
  const locale = typeof window !== 'undefined' 
    ? (window.localStorage.getItem('page-locale') || 'en') 
    : 'en'

  const [email, setEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()
  const handleSubscribe = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault()

    if (!email) {
      toast({
        title: translations[locale]?.footer.subscribe.placeholder,
        variant: 'destructive',
      })
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/form-submissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          form: process.env.NEXT_PUBLIC_NEWSLETTER_FORM_ID,
          submissionData: [
            {
              field: 'email',
              value: email,
            },
          ],
        }),
      })

      if (!response.ok) {
        throw new Error(translations[locale]?.footer.subscribe.error)
      }

      setEmail('')

      toast({
        title: translations[locale]?.footer.subscribe.success,
        description: translations[locale]?.footer.subscribe.successInfo,
      })
    } catch (error) {
      toast({
        title: translations[locale]?.footer.subscribe.error,
        description: translations[locale]?.footer.subscribe.errorInfo,
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  return (
    <footer className="px-2 pb-10 pt-20 sm:container">
      <h2 className="sr-only">Footer</h2>
      <div className="container">
        <div className="xl:grid xl:grid-cols-4 xl:gap-8">
          <div className="block xl:block">
            <Logo hideWordmark={false} />
            <p className="mt-3 text-xs text-muted-foreground">
              {/* The app that’s redefining travel in China. */}
              {translations[locale]?.footer.subscribe.info}
            </p>
          </div>
          {/* <div className="grid grid-cols-2 gap-8 md:grid-cols-4 lg:col-span-3">
            {FOOTER_LINKS.map((group) => (
              <div key={group.title}>
                <h3 className="text-sm font-semibold text-foreground">{group.title}</h3>
                <ul role="list" className="mt-6 space-y-2">
                  {group.links.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        title={link.name}
                        target={link.external ? '_blank' : undefined}
                        rel={link.external ? 'noopener noreferrer' : undefined}
                        className="relative text-sm text-muted-foreground transition-colors hover:text-foreground"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div> */}
          <div className="mt-10 space-y-4 lg:col-span-2 xl:mt-0">
            <h3 className="text-sm font-semibold text-foreground">
              {/* Subscribe to our newsletter */}
              {translations[locale]?.footer.subscribe.title}
            </h3>
            <form className="py-2 sm:flex sm:max-w-md">
              <div className="w-full min-w-0">
                <Input
                  type="email"
                  placeholder={translations[locale]?.footer.subscribe.placeholder}
                  className="w-full"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isSubmitting}
                />
              </div>
              <div className="mt-3 sm:ml-4 sm:mt-0 sm:shrink-0">
                <Button type="submit" disabled={isSubmitting} onClick={handleSubscribe}>
                  {/* Subscribe */}
                  {translations[locale]?.footer.subscribe.button}
                </Button>
              </div>
            </form>
          </div>
        </div>
        <div className="mt-8 border-t pt-8">
          <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
            <p className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} {APP_NAME}. All rights reserved.
            </p>
            <div className="flex flex-row items-center gap-4">
              {SOCIAL_LINKS.map((link) => (
                <Link
                  key={link.name}
                  title={link.name}
                  href={link.href}
                  className="text-muted-foreground transition-colors hover:text-foreground"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <span className="sr-only">{link.name}</span>
                  {link.icon}
                </Link>
              ))}
              <Separator orientation="vertical" className="h-4" />
              <ThemeSelector />
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
