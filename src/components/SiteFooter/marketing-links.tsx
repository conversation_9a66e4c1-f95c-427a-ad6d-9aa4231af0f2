import * as React from 'react'
import {
  BookIcon,
  BookOpenIcon,
  BoxIcon,
  CircuitBoardIcon,
  CuboidIcon,
  FileBarChartIcon,
  LayoutIcon,
  PlayIcon,
  SendHorizonalIcon,
} from 'lucide-react'

import {
  FacebookIcon,
  InstagramIcon,
  LinkedInIcon,
  TikTokIcon,
  XIcon,
  YouTubeIcon,
} from '@/components/ui/brand-icons'

const baseUrl = {
  Dashboard: 'https://dashboard.easygo.com.cn',
  Marketing: 'https://marketing.easygo.com.cn',
}

const routes = {
  dashboard: {
    Api: `${baseUrl.Dashboard}/api`,
    auth: {
      changeEmail: {
        Expired: `${baseUrl.Dashboard}/auth/change-email/expired`,
        Index: `${baseUrl.Dashboard}/auth/change-email`,
        Invalid: `${baseUrl.Dashboard}/auth/change-email/invalid`,
        Request: `${baseUrl.Dashboard}/auth/change-email/request`,
      },
      Error: `${baseUrl.Dashboard}/auth/error`,
      forgetPassword: {
        Index: `${baseUrl.Dashboard}/auth/forgot-password`,
        Success: `${baseUrl.Dashboard}/auth/forgot-password/success`,
      },
      Index: `${baseUrl.Dashboard}/auth`,
      RecoveryCode: `${baseUrl.Dashboard}/auth/recovery-code`,
      resetPassword: {
        Expired: `${baseUrl.Dashboard}/auth/reset-password/expired`,
        Index: `${baseUrl.Dashboard}/auth/reset-password`,
        Request: `${baseUrl.Dashboard}/auth/reset-password/request`,
        Success: `${baseUrl.Dashboard}/auth/reset-password/success`,
      },
      SignIn: `${baseUrl.Dashboard}/auth/sign-in`,
      SignUp: `${baseUrl.Dashboard}/auth/sign-up`,
      Totp: `${baseUrl.Dashboard}/auth/totp`,
      verifyEmail: {
        Expired: `${baseUrl.Dashboard}/auth/verify-email/expired`,
        Index: `${baseUrl.Dashboard}/auth/verify-email`,
        Request: `${baseUrl.Dashboard}/auth/verify-email/request`,
        Success: `${baseUrl.Dashboard}/auth/verify-email/success`,
      },
    },
    Index: `${baseUrl.Dashboard}/`,
    invitations: {
      AlreadyAccepted: `${baseUrl.Dashboard}/invitations/already-accepted`,
      Index: `${baseUrl.Dashboard}/invitations`,
      Request: `${baseUrl.Dashboard}/invitations/request`,
      Revoked: `${baseUrl.Dashboard}/invitations/revoked`,
    },
    onboarding: {
      Index: `${baseUrl.Dashboard}/onboarding`,
      Organization: `${baseUrl.Dashboard}/onboarding/organization`,
      User: `${baseUrl.Dashboard}/onboarding/user`,
    },
    organizations: {
      Index: `${baseUrl.Dashboard}/organizations`,
      slug: {
        Contacts: `${baseUrl.Dashboard}/organizations/[slug]/contacts`,
        Home: `${baseUrl.Dashboard}/organizations/[slug]/home`,
        Index: `${baseUrl.Dashboard}/organizations/[slug]`,
        settings: {
          acccount: {
            Index: `${baseUrl.Dashboard}/organizations/[slug]/settings/account`,
            Notifications: `${baseUrl.Dashboard}/organizations/[slug]/settings/account/notifications`,
            Profile: `${baseUrl.Dashboard}/organizations/[slug]/settings/account/profile`,
            Security: `${baseUrl.Dashboard}/organizations/[slug]/settings/account/security`,
          },
          Index: `${baseUrl.Dashboard}/organizations/[slug]/settings`,
          organization: {
            Billing: `${baseUrl.Dashboard}/organizations/[slug]/settings/organization/billing`,
            Developers: `${baseUrl.Dashboard}/organizations/[slug]/settings/organization/developers`,
            General: `${baseUrl.Dashboard}/organizations/[slug]/settings/organization/general`,
            Index: `${baseUrl.Dashboard}/organizations/[slug]/settings/organization`,
            Members: `${baseUrl.Dashboard}/organizations/[slug]/settings/organization/members`,
          },
        },
      },
    },
  },
  marketing: {
    Api: `${baseUrl.Marketing}/api`,
    Blog: `${baseUrl.Marketing}/blog`,
    Careers: `${baseUrl.Marketing}/careers`,
    Contact: `${baseUrl.Marketing}/contact`,
    CookiePolicy: `${baseUrl.Marketing}/cookie-policy`,
    Docs: `${baseUrl.Marketing}/docs`,
    Index: `${baseUrl.Marketing}/`,
    Pricing: `${baseUrl.Marketing}/pricing`,
    PrivacyPolicy: `${baseUrl.Marketing}/privacy-policy`,
    Roadmap: 'https://achromatic.canny.io',
    Story: `${baseUrl.Marketing}/story`,
    TermsOfUse: `${baseUrl.Marketing}/terms-of-use`,
  },
} as const

export const MENU_LINKS = [
  {
    title: 'Product',
    items: [
      {
        title: 'Feature 1',
        description: 'Short description here',
        icon: <BoxIcon className="size-5 shrink-0" />,
        href: '~/',
        external: false,
      },
      {
        title: 'Feature 2',
        description: 'Short description here',
        icon: <PlayIcon className="size-5 shrink-0" />,
        href: '~/',
        external: false,
      },
      {
        title: 'Feature 3',
        description: 'Short description here',
        icon: <CircuitBoardIcon className="size-5 shrink-0" />,
        href: '~/',
        external: false,
      },
      {
        title: 'Feature 4',
        description: 'Short description here',
        icon: <LayoutIcon className="size-5 shrink-0" />,
        href: '~/',
        external: false,
      },
      {
        title: 'Feature 5',
        description: 'Short description here',
        icon: <FileBarChartIcon className="size-5 shrink-0" />,
        href: '~/',
        external: false,
      },
    ],
  },
  {
    title: 'Resources',
    items: [
      {
        title: 'Contact',
        description: 'Reach out for assistance',
        icon: <SendHorizonalIcon className="size-5 shrink-0" />,
        href: routes.marketing.Contact,
        external: false,
      },
      {
        title: 'Roadmap',
        description: 'See what is coming next',
        icon: <LayoutIcon className="size-5 shrink-0" />,
        href: routes.marketing.Roadmap,
        external: true,
      },
      {
        title: 'Docs',
        description: 'Learn how to use our platform',
        icon: <BookOpenIcon className="size-5 shrink-0" />,
        href: routes.marketing.Docs,
        external: false,
      },
    ],
  },
  {
    title: 'Pricing',
    href: routes.marketing.Pricing,
    external: false,
  },
  {
    title: 'Blog',
    href: routes.marketing.Blog,
    external: false,
  },
  {
    title: 'Story',
    href: routes.marketing.Story,
    external: false,
  },
]

export const FOOTER_LINKS = [
  {
    title: 'Product',
    links: [
      { name: 'Feature 1', href: '~/', external: false },
      { name: 'Feature 2', href: '~/', external: false },
      { name: 'Feature 3', href: '~/', external: false },
      { name: 'Feature 4', href: '~/', external: false },
      { name: 'Feature 5', href: '~/', external: false },
    ],
  },
  {
    title: 'Resources',
    links: [
      { name: 'Contact', href: routes.marketing.Contact, external: false },
      { name: 'Roadmap', href: routes.marketing.Roadmap, external: true },
      { name: 'Docs', href: routes.marketing.Docs, external: false },
    ],
  },
  {
    title: 'About',
    links: [
      { name: 'Story', href: routes.marketing.Story, external: false },
      { name: 'Blog', href: routes.marketing.Blog, external: false },
      { name: 'Careers', href: routes.marketing.Careers, external: false },
    ],
  },
  {
    title: 'Legal',
    links: [
      {
        name: 'Terms of Use',
        href: routes.marketing.TermsOfUse,
        external: false,
      },
      {
        name: 'Privacy Policy',
        href: routes.marketing.PrivacyPolicy,
        external: false,
      },
      {
        name: 'Cookie Policy',
        href: routes.marketing.CookiePolicy,
        external: false,
      },
    ],
  },
]

export const SOCIAL_LINKS = [
  {
    name: 'X (formerly Twitter)',
    href: 'https://x.com/BestthingsChina',
    icon: <XIcon className="size-4 shrink-0" />,
  },
  {
    name: 'Facebook',
    href: 'https://www.facebook.com/easygo.china',
    icon: <FacebookIcon className="size-4 shrink-0" />,
  },
  {
    name: 'Youtube',
    href: 'https://www.youtube.com/@EasyGoChina',
    icon: <YouTubeIcon className="size-4 shrink-0" />,
  },
  {
    name: 'Instagram',
    href: 'https://www.instagram.com/easygochinaapp',
    icon: <InstagramIcon className="size-4 shrink-0" />,
  },
  {
    name: 'TikTok',
    href: 'https://www.tiktok.com/@easygo_china',
    icon: <TikTokIcon className="size-4 shrink-0" />,
  },
]

export const DOCS_LINKS = [
  {
    title: 'Getting Started',
    icon: <CuboidIcon className="size-4 shrink-0 text-muted-foreground" />,
    items: [
      {
        title: 'Introduction',
        href: '/docs',
        items: [],
      },
      {
        title: 'Dependencies',
        href: '/docs/dependencies',
        items: [],
      },
    ],
  },
  {
    title: 'Guides',
    icon: <BookIcon className="size-4 shrink-0 text-muted-foreground" />,
    items: [
      {
        title: 'Using MDX',
        href: '/docs/using-mdx',
        items: [],
      },
    ],
  },
]
