/* eslint-disable react/no-unescaped-entities */
import { Compass, Heart, Utensils, Globe, Smartphone } from 'lucide-react'
import Image from 'next/image'
import { Card } from '@/components/ui/card'

function FeatureCard({
  icon,
  title,
  description,
}: {
  icon: React.ReactNode
  title: string
  description: string
}) {
  return (
    <Card className="p-6 space-y-4 hover:border-primary/50 transition-colors">
      <div className="flex items-center gap-2">
        {icon}
        <h3 className="text-xl font-semibold text-primary dark:text-primary/90">{title}</h3>
      </div>
      <p className="text-muted-foreground">{description}</p>
    </Card>
  )
}

export default function AppFeature() {
  return (
    <div className="space-y-8">
      <div className="text-center space-y-8">
        <h2 className="text-xl dark:text-white">
          If you're interested, allow me to introduce our upcoming app
          {/* -<span className="text-2xl font-semibold text-primary">EasyGo China!</span> */}
        </h2>

        {/* 添加系统 Logo */}
        <div className="flex flex-col items-center gap-4">
          <Image
            alt="EasyGo China Logo"
            width={80}
            height={80}
            src="/images/app-logo.jpg"
            priority
            className="size-20"
          />
          <div className="text-primary font-bold text-xl">EasyGo China</div>
        </div>
      </div>

      <div className="space-y-6">
        <p className="text-lg dark:text-gray-300">
          EasyGo China, the app that's redefining travel in China, is designed to solve the
          challenges foreign travelers often face, such as finding authentic cuisine, exploring the
          most stunning attractions, and discovering hidden gems. It delivers:
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <FeatureCard
          icon={<Smartphone className="text-primary dark:text-primary/80" />}
          title="Guideless Freedom, Guided Convenience"
          description="With just your smartphone, enjoy the ease of a guided tour while keeping the flexibility to set your own pace and break free from rigid group tour schedules."
        />

        <FeatureCard
          icon={<Heart className="text-primary dark:text-primary/80" />}
          title="Cultural Immersion"
          description="Not just landmarks; each day is designed around a cultural theme with thoughtfully arranged sights and deep storytelling."
        />

        <FeatureCard
          icon={<Compass className="text-primary dark:text-primary/80" />}
          title="Hidden Gems"
          description="Explore not only iconic attractions but also secret spots only seasoned travelers know."
        />

        <FeatureCard
          icon={<Utensils className="text-primary dark:text-primary/80" />}
          title="Authentic Cuisine"
          description="Hidden eateries, fusion cuisine, Michelin restaurants... We not only recommend authentic food along the way but also provide detailed introductions to ingredients, cooking methods, flavors, and stories."
        />

        <FeatureCard
          icon={<Globe className="text-primary dark:text-primary/80" />}
          title="All-in-One Features"
          description="Integrated English navigation maps, AI assistant, and audio guides ensure you won't need to switch between multiple apps during your journey."
        />
      </div>
    </div>
  )
}
