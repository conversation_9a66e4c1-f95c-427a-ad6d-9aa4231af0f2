/* eslint-disable react/no-unescaped-entities */
import Image from 'next/image'

export default function OurStory() {
  return (
    <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
      <h2 className="text-3xl text-primary font-bold mb-6 dark:text-white">Our Story</h2>
      <div className="space-y-8">
        <p className="text-lg dark:text-gray-300">
          If you've made it this far, we'd like to share our story with you.
        </p>

        <div className="space-y-6">
          <p className="dark:text-gray-300">
            One day in mid-2024, my wife's friend <PERSON>, from Chicago, planned a short trip to
            Beijing. Unfortunately, my wife and I weren't available to guide her personally. So, we
            decided to create a custom travel guide for her.
          </p>

          <p className="dark:text-gray-300">
            This wasn't just any guide—it was a day-long themed itinerary meticulously planned to
            ensure her enjoyment. We included instructions for ticket purchases, public transport
            tips, and a cultural context for every attraction.
          </p>

          <p className="dark:text-gray-300">
            For dining, we addressed <PERSON>'s unfamiliarity with Chinese menus by recommending three
            restaurants per meal, each with a curated selection of 5–7 dishes tailored to foreign
            tastes. We detailed the ingredients, flavors, and dining methods for each dish.
          </p>

          <p className="dark:text-gray-300">
            We even added dozens of unique cafes, tea shops, and bars to round out her experience.
          </p>

          <p className="dark:text-gray-300">
            Creating this guide turned out to be far more complex than we'd imagined, taking over
            two weeks of our free time. But Janet's reaction made it all worthwhile:
          </p>

          <blockquote className="border-l-4 border-primary pl-4 italic dark:text-gray-300">
            "What is this? Did you write a book for me? This is amazing!"
          </blockquote>

          <p className="dark:text-gray-300">After her first day, she messaged us:</p>

          <blockquote className="border-l-4 border-primary pl-4 italic dark:text-gray-300">
            "This is incredible! It feels like you're right here with me. This is way better than a
            tour guide—I love it!"
          </blockquote>

          <p className="dark:text-gray-300">
            Inspired by Janet's enthusiasm, we decided to test our concept further. Over two months,
            we created additional guides, launched a website, and recruited volunteers on Reddit.
            These volunteers, like Janet, explored Beijing armed with nothing more than their
            internet-connected phones and our guides. Their feedback? Unanimously enthusiastic!
          </p>

          <div className="space-y-4 pl-6">
            <blockquote className="text-muted-foreground">
              "So much content online is either inaccurate or takes forever to sift through. Your
              guide saved me hours of research!"
            </blockquote>
            <blockquote className="text-muted-foreground">
              "Not only did I avoid tourist traps, but I discovered hidden gems I'd never have found
              on my own!"
            </blockquote>
            <blockquote className="text-muted-foreground">
              "This guide felt so thoughtfully structured—it turned my day into a seamless and
              meaningful cultural experience."
            </blockquote>
            <blockquote className="text-muted-foreground">
              "I'd love even more cultural insights—China's history is absolutely fascinating!"
            </blockquote>
          </div>

          <p className="dark:text-gray-300">And the section on food? It was a massive hit:</p>

          <blockquote className="text-muted-foreground pl-6">
            "Beyond dumplings, roast duck, and zhajiang noodles, I never realized Beijing had so
            many culinary treasures. Oh my gosh!"
          </blockquote>

          <div className="relative w-full aspect-[4/3] md:aspect-[16/9] my-8 rounded-lg overflow-hidden">
            <Image
              src="/images/foods-images.png"
              alt="Various Chinese dishes"
              fill
              className="object-contain md:object-cover"
            />
          </div>

          <p className="dark:text-gray-300">
            Thanks to these wonderful travelers, we gained both confidence and valuable insights.
            They highlighted areas for improvement: occasional connectivity issues, the lack of
            English navigation maps, and the inconvenience of reading text while on the move.
          </p>

          <p className="dark:text-gray-300">
            That's when we decided to create an app. Building on everything our users loved, we
            developed a user-friendly interface integrating English maps, real-time translation, and
            audio guides. Our goal? To ensure every traveler to China enjoys an easier, richer, and
            more immersive experience.
          </p>

          <div className="bg-primary/5 dark:bg-primary/10 p-6 rounded-lg">
            <p className="text-primary/80 dark:text-primary/90">
              We named it EasyGo China, because it does exactly that—makes traveling in China easy.
              Whether it's exploring authentic Chinese culture, savoring local delicacies, or
              navigating hidden gems, this app is designed to elevate your journey.
            </p>
          </div>

          <p className="dark:text-gray-300">
            Imagine having the freedom of independent travel paired with the insights of a
            knowledgeable guide, without the hassle of group tours—no waiting for unreliable
            companions, no enduring activities you don't enjoy, no bland meals.
          </p>

          <p className="dark:text-gray-300">Our beta testers unanimously agreed:</p>

          <blockquote className="border-l-4 border-primary pl-4 italic dark:text-gray-300">
            "This is exactly what independent travelers need! Complete control over your schedule,
            no time wasted planning, and one guide gives you everything you need for an amazing day
            of eating, sightseeing, and adventure!"
          </blockquote>

          <p className="dark:text-gray-300">
            And all of this? For less than the price of a cocktail.
          </p>

          <div className="space-y-4">
            <p className="dark:text-gray-300">
              In our 1.0 release, you'll find over 30 one-day itineraries, each designed around the
              cultural essence of China. Every day is carefully structured with a blend of must-see
              attractions, hidden spots, and culinary highlights, giving you the perfect balance of
              freedom and guidance. And this is just the beginning—more incredible journeys are
              already in the works.
            </p>

            <p className="dark:text-gray-300">
              In the upcoming 2.0 version, we're taking it further with social features. You'll be
              able to connect with like-minded travelers, explore together, and share your
              experiences.
            </p>
          </div>

          <div className="space-y-4">
            <p className="dark:text-gray-300">
              As you use our tours, feel free to reach out via email with any questions. And if you
              have suggestions or feedback, we'd love to hear from you. Together, we can continue
              improving and delivering the best travel experiences possible.
            </p>

            <p className="dark:text-gray-300 font-medium">
              We can't wait to share this journey with you! Stay tuned.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
