/* eslint-disable react/no-unescaped-entities */

import Confetti from 'react-confetti'
import useWindowSize from 'react-use/lib/useWindowSize'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { useState } from 'react'
import { useToast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { CheckCircle } from 'lucide-react'

interface GetGiftProps {
  formId?: string
}

export default function GetGift(props: GetGiftProps) {
  const { formId } = props
  const { toast } = useToast()
  const [email, setEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const { width, height } = useWindowSize()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email) {
      toast({
        title: 'Please enter your email',
        variant: 'destructive',
      })
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/form-submissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          form: formId,
          submissionData: [
            {
              field: 'email',
              value: email,
            },
          ],
        }),
      })

      if (!response.ok) {
        throw new Error('Submission failed')
      }

      setIsSubmitted(true)

      toast({
        title: 'Success!',
        description: "We've sent the guide to your email. Please check your inbox!",
      })
    } catch (error) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  return (
    <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg relative">
      <h2 className="text-2xl font-bold mb-6 text-primary dark:text-white">How to Get Your Gift</h2>

      <div className="space-y-6 relative">
        {isSubmitted && (
          <Confetti
            width={width * 0.8}
            height={700}
            numberOfPieces={300}
            recycle={false}
            gravity={0.15}
            wind={0.05}
            style={{
              position: 'absolute',
              top: '-50px',
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 50,
              pointerEvents: 'none',
            }}
          />
        )}
        <p className="text-lg dark:text-gray-300">
          Ready to start your unforgettable journey in Harbin? Follow these simple steps to get your
          free self-guided tour and experience what EasyGo China has to offer!
        </p>

        <div className="grid gap-4">
          <div className="flex items-start gap-4">
            <Badge className="mt-1">1</Badge>
            <div>
              <h4 className="font-medium">Enter your email below</h4>
              <p className="text-sm text-muted-foreground">
                We'll send the guide right to your inbox
              </p>
            </div>
          </div>

          <div className="flex items-start gap-4">
            <Badge className="mt-1">2</Badge>
            <div>
              <h4 className="font-medium">Click "Get It Now"</h4>
              <p className="text-sm text-muted-foreground">Make sure your email is correct</p>
            </div>
          </div>

          <div className="flex items-start gap-4">
            <Badge className="mt-1">3</Badge>
            <div>
              <h4 className="font-medium">Check your email for the PDF guide</h4>
              <p className="text-sm text-muted-foreground">Start planning your magical journey!</p>
            </div>
          </div>
        </div>

        <Alert>
          <AlertDescription className="text-primary/80 dark:text-primary/90">
            Since our app is not yet released, we can only provide this simple format for now. But
            we guarantee it will still leave you with a deep and unforgettable impression!
          </AlertDescription>
        </Alert>

        <Card className="p-6 relative">
          {isSubmitted ? (
            <div className="space-y-4 text-center">
              <div className="flex justify-center">
                <CheckCircle className="h-12 w-12 text-primary dark:text-green-500" />
              </div>
              <div className="space-y-2">
                <h3 className="text-xl font-semibold text-primary dark:text-white">
                  Guide Sent Successfully!
                </h3>
                <p className="text-muted-foreground">
                  Please check your inbox for the PDF guide. If you don't see it, please check your
                  spam folder.
                </p>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                placeholder="Your Email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isSubmitting}
                className="focus:border-primary focus:ring-primary"
              />
              <Button
                className="w-full bg-primary hover:bg-primary/90 dark:bg-primary/80"
                size="lg"
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Sending...' : 'Get It Now'}
              </Button>
              <p className="text-sm text-muted-foreground text-center">
                We'll send the PDF guide directly to your inbox
              </p>
            </form>
          )}
        </Card>

        <div className="space-y-2 text-sm text-muted-foreground">
          <p>Usage Notes:</p>
          <ul className="list-disc pl-5 space-y-1">
            <li>
              This self-guided tour PDF provides detailed full-day itinerary. Use it as you wish.
            </li>
            <li>We recommend booking tickets in advance as suggested.</li>
            <li>
              Users are responsible for their own actions while using this guide. The service
              provider assumes no liability. Please follow local laws and regulations.
            </li>
            <li>
              The guide can be used offline, but ensure your phone is fully charged. For support,
              contact the email provided in the PDF.
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}
