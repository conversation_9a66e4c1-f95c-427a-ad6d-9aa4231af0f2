import { Mail } from 'lucide-react'
import Image from 'next/image'

export default function ContactInformation() {
  return (
    <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
      <h2 className="text-2xl font-bold mb-8 text-primary dark:text-white text-center">
        Contact Information
      </h2>

      <div className="space-y-8">
        {/* Email */}
        <div className="flex items-center justify-center gap-2 text-primary dark:text-primary/90">
          <Mail className="h-5 w-5" />
          <a href="mailto:<EMAIL>" className="hover:underline">
            <EMAIL>
          </a>
        </div>

        {/* QR Codes */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-[280px] md:max-w-sm mx-auto">
          {/* WhatsApp */}
          <div className="space-y-3">
            <div className="relative aspect-square overflow-hidden rounded-xl border-2 border-primary/20">
              <Image
                src="/images/whatsapp.jpg"
                alt="WhatsApp QR Code"
                fill
                className="object-cover"
              />
            </div>
            <p className="text-center text-sm font-medium text-primary/80 dark:text-primary/90">
              WhatsApp
            </p>
          </div>

          {/* WeChat */}
          <div className="space-y-3">
            <div className="relative aspect-square overflow-hidden rounded-xl border-2 border-primary/20">
              <Image src="/images/wechat.jpg" alt="WeChat QR Code" fill className="object-cover" />
            </div>
            <p className="text-center text-sm font-medium text-primary/80 dark:text-primary/90">
              WeChat
            </p>
          </div>
        </div>

        <p className="text-sm text-muted-foreground text-center">
          Scan the QR code to connect with us on WhatsApp or WeChat
        </p>
      </div>
    </div>
  )
}
