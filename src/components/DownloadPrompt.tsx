'use client'

import { Card, CardContent } from '@/components/ui/card'
import DownloadButtons from '@/components/DownloadButtons'

export default function DownloadPrompt() {
  return (
    <section
      id="download"
      className="py-8 bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden"
    >
      {/* 装饰性背景元素 */}
      <div className="absolute inset-0 opacity-30">
        <div className="w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1)_0%,transparent_50%),radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.1)_0%,transparent_50%)]"></div>
      </div>
      <div className="container mx-auto px-3 sm:px-4 lg:px-8">
        <Card className="max-w-4xl mx-auto bg-white/95 backdrop-blur-sm shadow-2xl">
          <CardContent className="p-4 sm:p-6 lg:p-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6 items-start">
              {/* Left side - Advertisement text */}
              <div className="lg:col-span-2 space-y-3 sm:space-y-4">
                <div className="bg-gradient-to-r from-red-50 to-orange-50 p-3 sm:p-4 rounded-lg border-l-4 border-brand-primary shadow-brand/20">
                  <p className="text-sm sm:text-base lg:text-lg text-gray-800 leading-relaxed">
                    <strong>
                      New users receive a complimentary one-day tour experience valued at{' '}
                      <span className="text-brand-primary text-lg sm:text-xl lg:text-2xl font-bold bg-yellow-100 px-2 py-1 rounded italic">
                        $15.99
                      </span>
                      .
                    </strong>
                    <br />
                    <span className="text-gray-600 text-xs sm:text-sm">
                      Your redemption code will be sent to your registered email address immediately
                      after registration.
                    </span>
                  </p>
                </div>

                {/* Fine Print */}
                <p className="text-xs text-gray-500">
                  * Offer valid for new users only. Terms and conditions apply.
                </p>
              </div>

              {/* Right side - Download Buttons */}
              <div className="mt-3">
                <DownloadButtons
                  direction="row"
                  size="sm"
                  containerClassName="lg:flex-col justify-center"
                  appStoreUrl="https://apps.apple.com/fr/app/easygo-china-travel-easy-fun/id6743195357"
                  googlePlayUrl="https://play.google.com/store/apps/details?id=com.easygochina.app"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  )
}
