'use client'

import { useTheme } from '@payloadcms/ui'
import Image from 'next/image'

import { cn } from '@/utilities/cn'

// The logo size below is 28px x 28px but in a 36px x 36px container.
// Because of the 8px difference the components <Sidebar /> and <Mobilesheet /> have a pl-0.5 (4px left padding) class applied.
// When you update the logo make sure to eventually adjust the pl-0.5 class in those two components.

export type LogoProps = React.HTMLAttributes<HTMLDivElement> & {
  hideSymbol?: boolean
  hideWordmark?: boolean
}

export function Logo({
  hideSymbol,
  hideWordmark = true,
  className,
  ...other
}: LogoProps): React.JSX.Element {
  const { theme } = useTheme()
  const logoUrl = theme === 'dark' ? '/logo-dark.png' : '/logo-light.png'
  return (
    <div className={cn('flex items-center space-x-2', className)} {...other}>
      {!hideSymbol && (
        <div className="flex size-8 items-center justify-center">
          <div className="flex size-7 items-center justify-center rounded-md text-primary-foregroun">
            <Image
              alt="EasyGo China Logo"
              width={28}
              height={28}
              src={logoUrl}
              priority
              className="size-7"
            />
          </div>
        </div>
      )}
      {!hideWordmark && <span className="font-bold">EasyGo China</span>}
    </div>
  )
}

export default Logo
