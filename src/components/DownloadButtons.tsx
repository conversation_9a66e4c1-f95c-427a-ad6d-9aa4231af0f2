'use client'

import { But<PERSON> } from '@/components/ui/button'

// Apple Logo SVG Component
const AppleIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
  </svg>
)

// Android Logo SVG Component
const AndroidIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M17.523 15.3414c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993.0001.5511-.4482.9997-.9993.9997m-11.046 0c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993 0 .5511-.4482.9997-.9993.9997m11.4045-6.02l1.9973-3.4592a.416.416 0 00-.1518-.5972.416.416 0 00-.5972.1518l-2.0223 3.5046C15.5889 8.2231 13.8375 7.8529 12 7.8529s-3.5889.3702-5.1879 1.0067L4.7898 5.3549a.416.416 0 00-.5972-.1518.416.416 0 00-.1518.5972L6.0361 9.3214C3.6621 10.7447 2 13.2716 2 16.1436v.8569c0 .4715.3823.8538.8538.8538h18.2924c.4715 0 .8538-.3823.8538-.8538v-.8569c0-2.872-1.6621-5.3989-4.0361-6.8225" />
  </svg>
)

interface DownloadButtonsProps {
  /** 布局方向 */
  direction?: 'row' | 'col'
  /** 按钮大小变体 */
  size?: 'sm' | 'md' | 'lg' | 'xl'
  /** 自定义容器类名 */
  containerClassName?: string
  /** 自定义按钮类名 */
  buttonClassName?: string
  /** 是否显示悬停效果 */
  showHoverEffects?: boolean
  /** App Store 链接 */
  appStoreUrl?: string
  /** Google Play 链接 */
  googlePlayUrl?: string
}

export default function DownloadButtons({
  direction = 'row',
  size = 'md',
  containerClassName = '',
  buttonClassName = '',
  showHoverEffects = true,
  appStoreUrl = '#',
  googlePlayUrl = '#',
}: DownloadButtonsProps) {
  // 根据size设置不同的样式
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          height: 'h-10 sm:h-12',
          padding: 'px-3 sm:px-4',
          gap: 'gap-2',
          iconSize: 'size-4 sm:size-5',
          textSize: 'text-xs',
          subtitleSize: 'text-xs',
        }
      case 'md':
        return {
          height: 'h-12 sm:h-14',
          padding: 'px-4 sm:px-6',
          gap: 'gap-2 sm:gap-3',
          iconSize: 'size-5 sm:size-6',
          textSize: 'text-xs',
          subtitleSize: 'text-xs sm:text-sm',
        }
      case 'lg':
        return {
          height: 'h-12 sm:h-16 lg:h-20',
          padding: 'px-4 sm:px-6',
          gap: 'gap-2 sm:gap-4',
          iconSize: 'size-6 sm:size-8',
          textSize: 'text-xs sm:text-sm',
          subtitleSize: 'text-sm sm:text-lg',
        }
      case 'xl':
        return {
          height: 'h-14 sm:h-18 lg:h-22',
          padding: 'px-6 sm:px-8',
          gap: 'gap-3 sm:gap-4',
          iconSize: 'size-8 sm:size-10',
          textSize: 'text-sm sm:text-base',
          subtitleSize: 'text-base sm:text-xl',
        }
      default:
        return {
          height: 'h-12 sm:h-14',
          padding: 'px-4 sm:px-6',
          gap: 'gap-2 sm:gap-3',
          iconSize: 'size-5 sm:size-6',
          textSize: 'text-xs',
          subtitleSize: 'text-xs sm:text-sm',
        }
    }
  }

  const sizeClasses = getSizeClasses()

  // 容器方向类名
  const directionClass = direction === 'row' ? 'flex-row' : 'flex-col'

  // 基础按钮样式
  const baseButtonClass = `
        ${sizeClasses.height} 
        ${sizeClasses.padding} 
        rounded-xl 
        shadow-lg 
        transition-all 
        duration-300 
        flex 
        items-center 
        justify-center 
        ${sizeClasses.gap}
        ${direction === 'row' ? 'flex-1 sm:flex-none max-w-[160px] sm:max-w-none' : ''}
        ${showHoverEffects ? 'hover:shadow-xl transform hover:scale-105' : ''}
        ${buttonClassName}
    `
    .trim()
    .replace(/\s+/g, ' ')

  const handleAppStoreClick = () => {
    if (appStoreUrl !== '#') {
      window.open(appStoreUrl, '_blank')
    }
  }

  const handleGooglePlayClick = () => {
    if (googlePlayUrl !== '#') {
      window.open(googlePlayUrl, '_blank')
    }
  }

  return (
    <div className={`flex ${directionClass} gap-3 sm:gap-4 ${containerClassName}`}>
      <Button
        className={`bg-black hover:bg-gray-800 text-white ${baseButtonClass}`}
        onClick={handleAppStoreClick}
      >
        <AppleIcon className={`${sizeClasses.iconSize} flex-shrink-0`} />
        <div className="flex flex-col items-start leading-tight">
          <span className={`${sizeClasses.textSize} opacity-80`}>Download on the</span>
          <span className={`${sizeClasses.subtitleSize} font-semibold`}>App Store</span>
        </div>
      </Button>

      <Button
        className={`bg-green-600 hover:bg-green-700 text-white ${baseButtonClass}`}
        onClick={handleGooglePlayClick}
      >
        <AndroidIcon className={`${sizeClasses.iconSize} flex-shrink-0`} />
        <div className="flex flex-col items-start leading-tight">
          <span className={`${sizeClasses.textSize} opacity-80`}>Get it on</span>
          <span className={`${sizeClasses.subtitleSize} font-semibold`}>Google Play</span>
        </div>
      </Button>
    </div>
  )
}
