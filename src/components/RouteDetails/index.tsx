import React from 'react'
import { Clock, Navigation } from 'lucide-react'

interface RouteDetailsProps {
  route: any
  mode: string
}

export default function RouteDetails({ route, mode }: RouteDetailsProps) {
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    }
    return `${minutes}分钟`
  }

  const formatDistance = (meters: number) => {
    if (meters >= 1000) {
      return `${(meters / 1000).toFixed(1)}公里`
    }
    return `${meters}米`
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center justify-between p-4 bg-accent rounded-lg">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            <span>预计时间</span>
          </div>
          <span className="font-medium">{formatDuration(route.time)}</span>
        </div>

        <div className="flex items-center justify-between p-4 bg-accent rounded-lg">
          <div className="flex items-center gap-2">
            <Navigation className="h-5 w-5" />
            <span>总距离</span>
          </div>
          <span className="font-medium">{formatDistance(route.distance)}</span>
        </div>
      </div>

      <div className="space-y-3">
        <h3 className="font-medium">导航详情</h3>
        <div className="space-y-2 max-h-[calc(100vh-450px)] overflow-y-auto pr-2">
          {route.steps.map((step: any, index: number) => (
            <div
              key={index}
              className="p-4 bg-accent/50 rounded-lg text-sm hover:bg-accent/70 transition-colors"
              dangerouslySetInnerHTML={{ __html: step.instruction }}
            />
          ))}
        </div>
      </div>
    </div>
  )
}
