'use client'

import { useState } from 'react'
import { toast } from 'sonner'

import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { CodeType } from '@/enums/code-type'
import { translations } from '@/languages/translations'

interface RegisterModalProps {
  isOpen: boolean
  locale: string
  onClose: () => void
}

export function RegisterModal({ isOpen, locale, onClose }: RegisterModalProps) {
  const [step, setStep] = useState<'register' | 'verify'>('register')
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    invitationCode: '',
  })
  const [verificationCode, setVerificationCode] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isFinished, setIsFinished] = useState(false)

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      if (formData.invitationCode) {
        const inviteResponse = await fetch(
          `/api/invitation-codes?where[name][equals]=${formData.invitationCode}`,
        )
        if (!inviteResponse.ok) {
          const error = await inviteResponse.json()
          return toast.error(error.error || 'Please try again later')
        }
        const data = await inviteResponse.json()
        if (data.totalDocs === 0) {
          return toast.error('Invite code not found')
        }
      }

      const response = await fetch('/api/code/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          type: CodeType.REGISTER,
        }),
      })

      if (response.ok) {
        setStep('verify')
      } else {
        const error = await response.json()
        toast.error(error.error || 'Please try again later')
      }
    } catch (error) {
      toast.error('Register failed, Please try again later')
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const response = await fetch('/api/code/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          code: verificationCode,
          type: CodeType.REGISTER,
        }),
      })
      if (!response.ok) {
        const error = await response.json()
        return toast.error(error.error || 'Please try again later')
      }

      const res = await fetch('/api/auth/sign-up', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (res.ok) {
        toast.success('Registration successful!')
        // onClose()
        setStep('register')
        setFormData({ email: '', password: '', invitationCode: '' })
        setVerificationCode('')
        setIsFinished(true)
      } else {
        const error = await res.json()
        toast.error(error.error || 'Please try again later')
      }
    } catch (error) {
      toast.error('Verification failed, Please try again later')
    } finally {
      setIsLoading(false)
    }
  }

  const handleOnClose = () => {
    setIsFinished(false)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleOnClose}>
      <DialogContent className="sm:max-w-md">
        {isFinished ? (
          <div>{translations[locale]?.landing.freeTrial.registerSuccess}</div>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle className="text-center">
                {step === 'register' ? 'Create Account' : 'Verify Email'}
              </DialogTitle>
            </DialogHeader>

            {step === 'register' ? (
              <form onSubmit={handleRegister} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Email</label>
                  <input
                    type="email"
                    required
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Enter your email"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Password</label>
                  <input
                    type="password"
                    required
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Enter your password"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Invite Code <span className="text-gray-500 text-xs">(Optional)</span>
                  </label>
                  <input
                    type="text"
                    value={formData.invitationCode}
                    onChange={(e) => setFormData({ ...formData, invitationCode: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Enter invite code"
                  />
                </div>

                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Registering...' : 'Register'}
                </button>
              </form>
            ) : (
              <form onSubmit={handleVerify} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Verification Code</label>
                  <input
                    type="text"
                    required
                    maxLength={4}
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, ''))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary text-center text-lg tracking-widest"
                    placeholder="0000"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Verification code sent to {formData.email}
                  </p>
                </div>

                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Verifying...' : 'Verify'}
                </button>

                <button
                  type="button"
                  onClick={() => setStep('register')}
                  className="w-full text-primary hover:text-primary/80"
                >
                  Back to Edit
                </button>
              </form>
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}
