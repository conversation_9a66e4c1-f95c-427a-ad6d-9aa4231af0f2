# EasyGo China Landing Page

这是一个全新的landing page，与原有组件完全分离，使用现代化的设计和科技感的配色方案。

## 页面结构

1. **LandingHero** - Hero区域
   - 产品slogan: "Your Ultimate Self-Guided Travel Companion in China"
   - 副标题强调美食+文化故事
   - 手机截图展示
   - 下载按钮

2. **ValueProposition** - 产品价值主张
   - 4个核心功能模块
   - 简洁的图标和描述

3. **UserTestimonials** - 用户评论和心声
   - 视频区域: "Loved by Travelers Around the World"
   - 评论区: "Trusted by users from 20+ countries"
   - 带国旗的用户评论

4. **HowToUse** - 如何使用app
   - 5个步骤的轮播展示
   - 使用apps目录的截图

5. **FreeTrialCTA** - 免费试用CTA
   - 更大更突出的设计
   - "Travel Like a Local. Free Self-Guided Tour for New Users (Worth $15.99)"

6. **OurStory** - 我们的故事
   - 精简版的创始故事
   - 移除了version 1.0和2.0部分

7. **ContactUs** - 联系我们
   - 微信、WhatsApp、Instagram、YouTube、Facebook

8. **FinalDownload** - 最终下载模块
   - 简化的下载区域

## 设计特点

- **颜色方案**: 以蓝色和灰色为主，保持素雅但有科技感
- **响应式设计**: 完全适配移动端和桌面端
- **动画效果**: 适度的hover效果和过渡动画
- **模块化**: 每个组件独立，易于维护

## 访问方式

- 新landing page: `/landing`
- 原始网站: `/`

## 文件结构

```
components/landing/
├── LandingPage.tsx          # 主页面组件
├── LandingHeader.tsx        # 导航头部
├── LandingHero.tsx          # Hero区域
├── ValueProposition.tsx     # 价值主张
├── UserTestimonials.tsx     # 用户评论
├── HowToUse.tsx            # 使用指南
├── FreeTrialCTA.tsx        # 免费试用CTA
├── OurStory.tsx            # 我们的故事
├── ContactUs.tsx           # 联系我们
├── FinalDownload.tsx       # 最终下载
└── README.md               # 说明文档
```

## 共享资源

- 使用 `public/` 目录中的图片资源
- 复用 `@/components/ui/` 中的基础组件
- 使用现有的 `useScrollAnimation` hook 