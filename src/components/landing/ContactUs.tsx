'use client'

import { useState } from 'react'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { MessageCircle, Phone, QrCode, YoutubeIcon, X } from 'lucide-react'
import {
  FacebookIcon,
  InstagramIcon,
  LinkedInIcon,
  TikTokIcon,
  XIcon,
  YouTubeIcon,
} from '@/components/ui/brand-icons'
import { translations } from '@/languages/translations'

const socialLinks = [
  {
    name: 'WeChat',
    icon: MessageCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    hoverColor: 'hover:bg-green-100',
    description: 'Chat with us on WeChat',
    handle: '@louiseliu',
    qrCodeUrl: '/qrcode/easygo-wechat.png',
    url: '',
    showQRCode: true,
  },
  {
    name: 'TikTok',
    icon: TikTokIcon,
    color: 'text-green-500',
    bgColor: 'bg-green-50',
    hoverColor: 'hover:bg-green-100',
    description: 'Follow us on TikTok',
    handle: '@easygo_china',
    qrCodeUrl: '/qrcode/easygo-tiktok.png',
    url: 'https://www.tiktok.com/@easygo_china',
    showQRCode: false,
  },
  {
    name: 'Instagram',
    icon: InstagramIcon,
    color: 'text-primary',
    bgColor: 'bg-red-50',
    hoverColor: 'hover:bg-red-100',
    description: 'Follow our travel stories',
    handle: '@easygochinaapp',
    qrCodeUrl: '/qrcode/easygo-instagram.png',
    url: 'https://www.instagram.com/easygochinaapp',
    showQRCode: false,
  },
  {
    name: 'YouTube',
    icon: YouTubeIcon,
    color: 'text-brand-secondary',
    bgColor: 'bg-red-50',
    hoverColor: 'hover:bg-red-100',
    description: 'Watch travel guides',
    handle: '@EasyGoChina',
    qrCodeUrl: '/qrcode/easygo-youtube.png',
    url: 'https://www.youtube.com/@EasyGoChina',
    showQRCode: false,
  },
  {
    name: 'Facebook',
    icon: FacebookIcon,
    color: 'text-brand-orange',
    bgColor: 'bg-orange-50',
    hoverColor: 'hover:bg-orange-100',
    description: 'Join our community',
    handle: 'EasyGo China',
    qrCodeUrl: '/qrcode/easygo-facebook.png',
    url: 'https://www.facebook.com/easygo.china',
    showQRCode: false,
  },
  {
    name: 'X',
    icon: XIcon,
    color: 'text-brand-orange',
    bgColor: 'bg-orange-50',
    hoverColor: 'hover:bg-orange-100',
    description: 'Follow us on X',
    handle: 'EasyGo China',
    qrCodeUrl: '/qrcode/easygo-x.png',
    url: 'https://x.com/BestthingsChina',
    showQRCode: false,
  },
]

export default function ContactUs() {
  const locale = typeof window !== 'undefined' 
    ? (window.localStorage.getItem('page-locale') || 'en') 
    : 'en'

  const [selectedQR, setSelectedQR] = useState<(typeof socialLinks)[0] | null>(null)

  const openQRModal = (social: (typeof socialLinks)[0]) => {
    if (!social.showQRCode) {
      return window.open(social.url)
    }
    setSelectedQR(social)
  }

  const closeQRModal = () => {
    setSelectedQR(null)
  }

  return (
    <section className="py-16 lg:py-20 bg-white">
      <div className="container mx-auto px-4 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            {/* Stay Connected */}
            {translations[locale]?.landing.contact.title}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {/* Follow us for travel tips, updates, and connect with fellow travelers */}
            {translations[locale]?.landing.contact.description}
          </p>
        </div>

        {/* Social Links Grid */}
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {socialLinks.map((social, index) => {
              const IconComponent = social.icon
              return (
                <Card
                  key={index}
                  className={`border-0 shadow-lg ${social.hoverColor} transition-all duration-300 hover:shadow-xl hover:-translate-y-1 cursor-pointer`}
                  onClick={() => openQRModal(social)}
                >
                  <CardContent className="p-6 text-center relative">
                    {/* QR Code Icon */}
                    <div className="absolute top-4 right-4">
                      <QrCode className="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" />
                    </div>

                    <div
                      className={`w-16 h-16 ${social.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}
                    >
                      <IconComponent className={`h-8 w-8 ${social.color}`} />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{social.name}</h3>
                    {/* <p className="text-gray-600 text-sm mb-3">{social.description}</p> */}
                    <p className="text-gray-600 text-sm mb-3">{translations[locale]?.landing.contact.socialLinks[index].description}</p>
                    <p className={`font-semibold ${social.color}`}>{social.handle}</p>
                    <p className="text-xs text-gray-400 mt-2">
                      {/* Click to view QR code */}
                      {translations[locale]?.landing.contact.code.open}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* QR Code Modal */}
        {selectedQR && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-8 max-w-sm w-full relative">
              {/* Close Button */}
              <button
                onClick={closeQRModal}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>

              {/* Modal Content */}
              <div className="text-center">
                <div
                  className={`w-16 h-16 ${selectedQR.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}
                >
                  <selectedQR.icon className={`h-8 w-8 ${selectedQR.color}`} />
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-2">{selectedQR.name}</h3>

                <p className="text-gray-600 text-sm mb-6">
                  {/* Scan QR code to connect */}
                  {translations[locale]?.landing.contact.code.scan}
                </p>

                {/* QR Code */}
                <div className="bg-white p-4 rounded-lg border-2 border-gray-100 mb-4">
                  <Image
                    src={selectedQR.qrCodeUrl}
                    alt={`${selectedQR.name} QR Code`}
                    width={200}
                    height={200}
                    className="mx-auto"
                  />
                </div>

                <p className={`font-semibold ${selectedQR.color} mb-2`}>{selectedQR.handle}</p>

                <p className="text-xs text-gray-500">{selectedQR.description}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}
