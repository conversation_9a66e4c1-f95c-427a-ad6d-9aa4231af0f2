'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Globe, Utensils, BookOpen, Smartphone, DollarSign } from 'lucide-react'
import { translations } from '@/languages/translations'

const features = [
  {
    icon: Globe,
    title: 'No Chinese Required',
    description:
      'Built-in navigation with multilingual explanations for local attractions. Navigate attractions, food, and transportation effortlessly without knowing Chinese.',
    color: 'text-primary',
    bgColor: 'bg-red-50',
  },
  {
    icon: Utensils,
    title: 'Authentic Food, Smart Spending',
    description:
      'High-value food guide with genuine local recommendations. Skip tourist traps and enjoy three authentic meals for the price of one tourist group dinner.',
    color: 'text-brand-orange',
    bgColor: 'bg-orange-50',
  },
  {
    icon: BookOpen,
    title: 'Stories Behind Every Stop',
    description:
      'More than just check-ins - deep cultural experiences. From imperial streets to hutong cafes, discover cultural surprises in every corner.',
    color: 'text-brand-gold',
    bgColor: 'bg-yellow-50',
  },
  {
    icon: Smartphone,
    title: 'One-Click Purchase, Instant Use',
    description:
      'No groups, no rushing - control your own journey. Buy and use immediately, perfect for spontaneous trips and true independent travel.',
    color: 'text-brand-secondary',
    bgColor: 'bg-red-50',
  },
  {
    icon: DollarSign,
    title: 'Only $9.99/Day - Affordable Excellence',
    description:
      'Save $70+ compared to group tours while gaining more freedom and authentic experiences. No hidden costs, avoid tourist traps.',
    color: 'text-green-600',
    bgColor: 'bg-green-50',
  },
]

export default function ValueProposition() {
  const locale = typeof window !== 'undefined' 
    ? (window.localStorage.getItem('page-locale') || 'en') 
    : 'en'
    
  return (
    <section className="py-12 lg:py-16 xl:py-20 bg-white">
      <div className="container mx-auto px-4 lg:px-6 xl:px-8">
        {/* Header */}
        <div className="text-center mb-8 lg:mb-10 xl:mb-12">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            {/* Why Choose EasyGo China? */}
            {translations[locale]?.landing.valueProposition.sectionTitle}
            </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {/* Experience China like never before with our comprehensive travel companion */}
            {translations[locale]?.landing.valueProposition.sectionDescription}
            </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 lg:gap-5 xl:gap-6">
          {features.map((feature, index) => {
            const IconComponent = feature.icon
            return (
              <Card
                key={index}
                className="border-0 shadow-md xl:shadow-lg hover:shadow-lg xl:hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5 xl:hover:-translate-y-1 rounded-lg xl:rounded-xl"
              >
                <CardContent className="p-2 lg:p-4 text-center">
                  <div
                    className={`w-14 h-14 lg:w-16 lg:h-16 ${feature.bgColor} rounded-full flex items-center justify-center mx-auto mb-3 lg:mb-4`}
                  >
                    <IconComponent className={`h-7 w-7 lg:h-8 lg:w-8 ${feature.color}`} />
                  </div>
                  <h3 className="text-lg lg:text-xl font-bold text-gray-900 mb-2 lg:mb-3">
                    {/* {feature.title} */}
                    {translations[locale]?.landing.valueProposition.features[index].title}
                  </h3>
                  <p className="text-sm lg:text-base text-gray-600 leading-relaxed">
                    {/* {feature.description} */}
                    {translations[locale]?.landing.valueProposition.features[index].description}
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </section>
  )
}
