"use client"

import LandingHeader from "./LandingHeader"
import LandingHero from "./LandingHero"
import ValueProposition from "./ValueProposition"
import UserTestimonials from "./UserTestimonials"
import HowToUse from "./HowToUse"
import FreeTrialCTA from "./FreeTrialCTA"
import OurStory from "./OurStory"
import ContactUs from "./ContactUs"
import FinalDownload from "./FinalDownload"

export default function LandingPage() {
    return (
        <>
            {/* Header */}
            <LandingHeader />

            <main className="min-h-screen pt-16">
                {/* 第一部分：Hero区域 */}
                <LandingHero />

                {/* 第二部分：产品价值主张 */}
                <section id="features">
                    <ValueProposition />
                </section>

                {/* 第三部分：用户评论和心声 */}
                <section id="testimonials">
                    <UserTestimonials />
                </section>

                {/* 第四部分：如何使用这个app */}
                <HowToUse />

                {/* 第五部分：免费试用/促销 CTA 区域 */}
                <section id="download">
                    <FreeTrialCTA />
                </section>

                {/* 第六部分：our story */}
                <section id="story">
                    <OurStory />
                </section>

                {/* 第七部分：联系我们 */}
                <section id="contact">
                    <ContactUs />
                </section>

                {/* 第八部分：再放一次下载的模块 */}
                <FinalDownload />
            </main>
        </>
    )
} 