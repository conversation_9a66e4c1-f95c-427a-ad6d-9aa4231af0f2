'use client'
import { translations } from '@/languages/translations'

export default function HowToUse() {
  const locale = typeof window !== 'undefined' 
    ? (window.localStorage.getItem('page-locale') || 'en') 
    : 'en'
  return (
    <section className="py-16 lg:py-20 bg-white relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute top-0 left-0 w-full h-full opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 bg-primary rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-brand-orange rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            {/* How to Use EasyGo China */}
            {translations[locale]?.landing.use.title}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {/* Simple steps to unlock the best of China.  */}
            {translations[locale]?.landing.use.description}
            <br />
            {/* From planning to exploring, we&apos;ve got you covered. */}
            {translations[locale]?.landing.use.description2}
          </p>
        </div>

        {/* YouTube Video */}
        <div className="max-w-4xl mx-auto">
          <div className="relative w-full" style={{ paddingBottom: '56.25%' }}>
            <iframe
              className="absolute top-0 left-0 w-full h-full rounded-lg shadow-2xl"
              src="https://www.youtube.com/embed/B3oYCb0466g"
              title="How to Use EasyGo China"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            ></iframe>
          </div>
        </div>
      </div>
    </section>
  )
}
