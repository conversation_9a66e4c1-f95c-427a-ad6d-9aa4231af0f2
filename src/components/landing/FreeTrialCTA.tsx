'use client'

import { useState } from 'react'
import { Toaster } from 'sonner'

import { Card, CardContent } from '@/components/ui/card'
import DownloadButtons from '@/components/DownloadButtons'
import { translations } from '@/languages/translations'
import { RegisterModal } from '../RegisterModal'

export default function FreeTrialCTA() {
  const locale =
    typeof window !== 'undefined' ? window.localStorage.getItem('page-locale') || 'en' : 'en'

  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false)

  return (
    <section className="py-16 lg:py-20 bg-gradient-to-br from-red-50 via-white to-orange-50 relative overflow-hidden">
      {/* 装饰性背景元素 */}
      <div className="absolute inset-0 opacity-30">
        <div className="w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(171,51,47,0.1)_0%,transparent_50%),radial-gradient(circle_at_70%_80%,rgba(234,88,12,0.1)_0%,transparent_50%)]"></div>
      </div>

      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        <Card className="max-w-6xl mx-auto bg-white/95 backdrop-blur-sm shadow-2xl border-0">
          <CardContent className="p-8 lg:p-12">
            <div className="text-center mb-8">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                {/* Travel Like a Local */}
                {translations[locale]?.landing.freeTrial.title}
              </h2>
              <div className="bg-gradient-to-r from-red-500 to-red-600 p-6 lg:p-8 rounded-2xl shadow-xl max-w-4xl mx-auto">
                <p className="text-xl lg:text-2xl text-white leading-relaxed mb-4">
                  <span className="bg-yellow-400 text-gray-900 px-3 py-1 rounded-full font-black text-xl lg:text-2xl mr-2 shadow-lg transform rotate-3 inline-block border-2 border-yellow-300">
                    {/* FREE */}
                    {translations[locale]?.landing.freeTrial.free}
                  </span>
                  <strong className="text-white">
                    {/* Self-Guided Tour for New Users */}
                    {translations[locale]?.landing.freeTrial.description}
                  </strong>
                  <br />
                  <span className="text-xl lg:text-2xl font-bold text-yellow-200 bg-red-700 px-3 py-2 rounded-lg inline-block mt-3">
                    {/* Worth $15.99 */}
                    {translations[locale]?.landing.freeTrial.price}
                  </span>
                </p>
                <p className="text-red-100 text-sm opacity-80">
                  {/* Your redemption code will be sent to your registered email address immediately
                  after registration. */}
                  {translations[locale]?.landing.freeTrial.content}
                </p>
              </div>
            </div>

            {/* 注册按钮 */}
            <div className="text-center mb-12">
              <button
                onClick={() => setIsRegisterModalOpen(true)}
                className="bg-primary text-white px-8 py-3 rounded-lg font-semibold text-lg hover:bg-primary/90 transition-colors shadow-lg"
              >
                Get Your Free Guide
              </button>
            </div>

            <div className="flex flex-col lg:flex-row gap-8 justify-center mt-16">
              {/* Left side - Benefits */}
              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {/* What You Get: */}
                  {translations[locale]?.landing.freeTrial.section}
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <span className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-primary text-sm">✓</span>
                    </span>
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {/* No Language Barriers */}
                        {translations[locale]?.landing.freeTrial.title1}
                      </h4>
                      <p className="text-gray-600 text-sm">
                        {/* Multilingual system lets you explore China with ease. */}
                        {translations[locale]?.landing.freeTrial.description1}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-primary text-sm">✓</span>
                    </span>
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {/* Local Food Discoveries */}
                        {translations[locale]?.landing.freeTrial.title2}
                      </h4>
                      <p className="text-gray-600 text-sm">
                        {/* 30+ authentic restaurants with ordering guides. */}
                        {translations[locale]?.landing.freeTrial.description2}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-primary text-sm">✓</span>
                    </span>
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {/* Cultural Insights */}
                        {translations[locale]?.landing.freeTrial.title3}
                      </h4>
                      <p className="text-gray-600 text-sm">
                        {/* 20,000+ words of cultural stories and tips. */}
                        {translations[locale]?.landing.freeTrial.description3}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-primary text-sm">✓</span>
                    </span>
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {/* Flexible Itineraries */}
                        {translations[locale]?.landing.freeTrial.title4}
                      </h4>
                      <p className="text-gray-600 text-sm">
                        {/* Play instantly. Pick your favorite spots from the self-guided tour and
                        explore freely. */}
                        {translations[locale]?.landing.freeTrial.description4}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right side - Download Buttons */}
              <div className="flex flex-col gap-4 lg:gap-6">
                <div className="text-center lg:text-left mb-4">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {/* Download Now & Start Exploring */}
                    {translations[locale]?.landing.freeTrial.download}
                  </h3>
                  <p className="text-gray-600">
                    {/* Available on all platforms */}
                    {translations[locale]?.landing.freeTrial.downloadText}
                  </p>
                </div>

                <DownloadButtons
                  direction="row"
                  size="md"
                  appStoreUrl="https://apps.apple.com/fr/app/easygo-china-travel-easy-fun/id6743195357"
                  googlePlayUrl="https://play.google.com/store/apps/details?id=com.easygochina.app"
                />

                {/* Trust Indicators */}
                <div className="flex flex-wrap justify-center lg:justify-start gap-4 text-sm text-gray-500 mt-4">
                  <span className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full"></span>
                    {/* No credit card required */}
                    {translations[locale]?.landing.freeTrial.card}
                  </span>
                  <span className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-primary rounded-full"></span>
                    {/* Instant access */}
                    {translations[locale]?.landing.freeTrial.cardText}
                  </span>
                </div>
              </div>
            </div>

            {/* Fine Print */}
            <div className="text-center mt-8 pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-500">
                {/* * Offer valid for new users only. Terms and conditions apply. */}*{' '}
                {translations[locale]?.landing.freeTrial.info}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 注册 Modal */}
      <RegisterModal
        isOpen={isRegisterModalOpen}
        locale={locale}
        onClose={() => setIsRegisterModalOpen(false)}
      />
      <Toaster position="top-center" richColors />
    </section>
  )
}
