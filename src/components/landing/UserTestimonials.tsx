'use client'

import { useState, useEffect, useRef } from 'react'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Play, X, ChevronLeft, ChevronRight } from 'lucide-react'
import { translations } from '@/languages/translations'

export default function UserTestimonials() {
  const locale = typeof window !== 'undefined' 
    ? (window.localStorage.getItem('page-locale') || 'en') 
    : 'en'

  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false)
  const [currentTestimonialIndex, setCurrentTestimonialIndex] = useState(0)
  const [isMobile, setIsMobile] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  // 评论数据 - 使用外部国旗图片链接
  const testimonials = [
    {
      text: 'EasyGo China made my trip absolutely seamless. The translation feature saved me countless times!',
      author: '<PERSON>',
      country: 'USA',
      flagUrl: 'https://flagcdn.com/w40/us.png',
      rating: 5,
    },
    {
      text: 'The local recommendations were spot-on. I discovered hidden gems I never would have found otherwise.',
      author: '<PERSON>',
      country: 'UK',
      flagUrl: 'https://flagcdn.com/w40/gb.png',
      rating: 5,
    },
    {
      text: "Best travel app I've ever used. The cultural insights helped me connect with locals authentically.",
      author: 'Maria G.',
      country: 'Spain',
      flagUrl: 'https://flagcdn.com/w40/es.png',
      rating: 5,
    },
    {
      text: 'An AI assistant can promptly translate and interpret some additional common knowledge questions.',
      author: 'Chen W.',
      country: 'Singapore',
      flagUrl: 'https://flagcdn.com/w40/sg.png',
      rating: 5,
    },
    {
      text: 'The built-in navigation made exploring totally hassle-free — super easy and convenient!',
      author: 'Alex R.',
      country: 'Canada',
      flagUrl: 'https://flagcdn.com/w40/ca.png',
      rating: 5,
    },
    {
      text: "Found the most amazing local restaurants that weren't in any guidebook. This app is a game-changer!",
      author: 'Emma K.',
      country: 'Australia',
      flagUrl: 'https://flagcdn.com/w40/au.png',
      rating: 5,
    },
  ]

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // 自动轮播
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonialIndex((prev) => {
        const maxIndex = isMobile ? testimonials.length - 1 : testimonials.length - 3
        return prev >= maxIndex ? 0 : prev + 1
      })
    }, 4000)
    return () => clearInterval(interval)
  }, [testimonials.length, isMobile])

  const nextTestimonial = () => {
    setCurrentTestimonialIndex((prev) => {
      const maxIndex = isMobile ? testimonials.length - 1 : testimonials.length - 3
      return prev >= maxIndex ? 0 : prev + 1
    })
  }

  const prevTestimonial = () => {
    setCurrentTestimonialIndex((prev) => {
      const maxIndex = isMobile ? testimonials.length - 1 : testimonials.length - 3
      return prev === 0 ? maxIndex : prev - 1
    })
  }

  const openVideoModal = () => {
    setIsVideoModalOpen(true)
    setTimeout(() => {
      if (videoRef.current) {
        const playPromise = videoRef.current.play()
        if (playPromise !== undefined) {
          playPromise
            .then(() => {
              console.log('Video started playing')
            })
            .catch((error) => {
              console.error('Auto-play failed:', error)
            })
        }
      }
    }, 100)
  }

  const closeVideoModal = () => {
    setIsVideoModalOpen(false)
    if (videoRef.current) {
      videoRef.current.pause()
      videoRef.current.currentTime = 0
    }
  }

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (isVideoModalOpen && event.key === 'Escape') {
        closeVideoModal()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isVideoModalOpen])

  // 防止模态框打开时页面滚动
  useEffect(() => {
    if (isVideoModalOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isVideoModalOpen])

  return (
    <section className="py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-red-50/20">
      <div className="container mx-auto px-4 lg:px-8">
        {/* Video Section */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              {/* Loved by Travelers Around the World */}
              {translations[locale]?.landing.userTestimonials.sectionTitle}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {/* Hear real stories from travelers who experienced China with EasyGo China */}
              {translations[locale]?.landing.userTestimonials.sectionDescription}
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div
              className="relative bg-black rounded-2xl overflow-hidden cursor-pointer group transition-transform duration-300 hover:scale-105 h-[300px] lg:h-[500px]"
              onClick={openVideoModal}
            >
              <Image
                src="/apps/10.jpeg"
                alt="Customer Travel Experience Stories"
                width={800}
                height={500}
                className="w-full h-full object-cover"
                onError={(e) => {
                  console.error('Image failed to load, trying backup')
                  e.currentTarget.src = '/apps/11.jpeg'
                }}
              />

              {/* Play Button Overlay */}
              <div className="absolute inset-0 bg-black/20 flex items-center justify-center group-hover:bg-opacity-30 transition-all duration-300">
                <div className="bg-black/80 opacity-60 text-white p-4 lg:p-6 rounded-full transition-all duration-300 transform group-hover:scale-110">
                  <Play className="h-8 w-8 lg:h-16 lg:w-16" />
                </div>
              </div>

              {/* Video Title Overlay */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                <h4 className="text-white text-lg lg:text-2xl font-semibold mb-2">
                  {/* Real Stories from Our Travelers */}
                  {translations[locale]?.landing.userTestimonials.sectionImageTitle}
                </h4>
                <p className="text-white/80 text-sm lg:text-base">
                  {/* Discover how EasyGo China transformed their travel experience */}
                  {translations[locale]?.landing.userTestimonials.sectionImageDescription}
                  </p>
              </div>
            </div>
          </div>
        </div>

        {/* Testimonials Section */}
        <div>
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              {/* Trusted by users from 20+ countries */}
              {translations[locale]?.landing.userTestimonials.sectionTitle2}
              </h3>
            <p className="text-lg text-gray-600">
              {/* Join thousands of satisfied travelers worldwide */}
              {translations[locale]?.landing.userTestimonials.sectionDescription2}
              </p>
          </div>

          <div className="relative max-w-6xl mx-auto">
            {/* 轮播容器 */}
            <div className="overflow-hidden">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{
                  transform: `translateX(-${currentTestimonialIndex * (isMobile ? 100 : 100 / 3)}%)`,
                }}
              >
                {testimonials.map((testimonial, index) => (
                  <div key={index} className="w-full md:w-1/3 flex-shrink-0 px-3">
                    <Card className="bg-white h-full shadow-lg hover:shadow-xl transition-shadow duration-300">
                      <CardContent className="p-6 h-full flex flex-col">
                        <div className="flex items-center mb-3">
                          <div className="flex text-yellow-400 text-sm">
                            {[...Array(testimonial.rating)].map((_, i) => (
                              <span key={i}>⭐</span>
                            ))}
                          </div>
                        </div>
                        <p className="text-gray-700 mb-4 text-sm lg:text-base flex-grow leading-relaxed">
                          &quot;{translations[locale]?.landing.userTestimonials.testimonials[index].text}&quot;
                        </p>
                        <div className="flex items-center gap-3">
                          <Image
                            src={testimonial.flagUrl}
                            alt={testimonial.country}
                            width={32}
                            height={22}
                            className="w-6 h-4 rounded-sm object-cover"
                          />
                          <div>
                            <div className="font-semibold text-gray-900 text-sm">
                              {testimonial.author}
                            </div>
                            <div className="text-xs text-gray-500">
                              {translations[locale]?.landing.userTestimonials.testimonials[index].country}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            </div>

            {/* 导航按钮 */}
            <button
              onClick={prevTestimonial}
              className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110"
            >
              <ChevronLeft className="h-6 w-6 text-gray-600" />
            </button>

            <button
              onClick={nextTestimonial}
              className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110"
            >
              <ChevronRight className="h-6 w-6 text-gray-600" />
            </button>

            {/* 指示器 */}
            <div className="flex justify-center mt-6 space-x-2">
              {Array.from({
                length: isMobile ? testimonials.length : testimonials.length - 2,
              }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonialIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-200 ${
                    index === currentTestimonialIndex
                      ? 'bg-primary w-8'
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Video Modal */}
        {isVideoModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-95 z-50 flex items-center justify-center p-4">
            <div className="relative max-w-5xl max-h-full w-full">
              {/* Close Button */}
              <button
                onClick={closeVideoModal}
                className="absolute -top-12 -right-4 text-white hover:text-gray-300 z-10 bg-black bg-opacity-50 p-3 rounded-full transition-colors duration-200"
              >
                <X className="h-6 w-6" />
              </button>

              {/* Video Container */}
              <div className="relative bg-black rounded-2xl overflow-hidden shadow-2xl">
                <video
                  ref={videoRef}
                  className="w-full h-auto"
                  poster="/apps/10.jpeg"
                  preload="metadata"
                  controls
                  autoPlay
                >
                  <source src="/videos/easygo.mp4" type="video/mp4" />
                  Your browser does not support video playback.
                </video>
              </div>

              {/* Video Title */}
              <div className="text-center mt-4">
                <h4 className="text-white text-xl font-semibold mb-2">
                  {/* Real Stories from Our Travelers */}
                  {translations[locale]?.landing.userTestimonials.videoTitle}
                </h4>
                <p className="text-gray-300 text-sm">
                  {/* Hear how real users rate their EasyGo China travel experience */}
                  {translations[locale]?.landing.userTestimonials.videoDescription}
                  </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}
