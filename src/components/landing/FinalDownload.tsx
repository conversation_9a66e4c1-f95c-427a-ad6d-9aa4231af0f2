'use client'

import DownloadButtons from '@/components/DownloadButtons'
import { translations } from '@/languages/translations'

export default function FinalDownload() {
  const locale = typeof window !== 'undefined' 
    ? (window.localStorage.getItem('page-locale') || 'en') 
    : 'en'

  return (
    <section className="py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-red-50 text-gray-900">
      <div className="container mx-auto px-4 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-900">
            {/* Ready to Explore China? */}
            {translations[locale]?.landing.finalDownload.title}
          </h2>
          <p className="text-xl lg:text-2xl mb-8 text-gray-600">
            {/* Download EasyGo China and start your adventure today */}
            {translations[locale]?.landing.finalDownload.description}
          </p>

          {/* Download Buttons */}
          <div className="mb-8">
            <DownloadButtons
              direction="row"
              size="md"
              containerClassName="justify-center"
              appStoreUrl="https://apps.apple.com/fr/app/easygo-china-travel-easy-fun/id6743195357"
              googlePlayUrl="https://play.google.com/store/apps/details?id=com.easygochina.app"
            />
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 md:grid-cols-3 gap-4 md:gap-8 mt-12">
            <div className="text-center">
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-primary mb-2">
                11+
              </div>
              <div className="text-gray-600 text-sm md:text-base">
                {/* Cities Available */}
                {translations[locale]?.landing.finalDownload.city}
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-primary mb-2">
                30+
              </div>
              <div className="text-gray-600 text-sm md:text-base">
                {/* Daily Itineraries */}
                {translations[locale]?.landing.finalDownload.daily}
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-primary mb-2">
                20+
              </div>
              <div className="text-gray-600 text-sm md:text-base">
                {/* Countries Trust Us */}
                {translations[locale]?.landing.finalDownload.country}
              </div>
            </div>
          </div>

          <div className="mt-8 text-gray-500">
            <p>
              {/* Join thousands of travelers discovering the real China */}
              {translations[locale]?.landing.finalDownload.info}
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
