'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Menu, X } from 'lucide-react'
import { translations } from '@/languages/translations'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

const locales = [
  {
    label: 'English',
    value: 'en',
  },
  {
    label: '한국어',
    value: 'ko',
  },
  {
    label: '日本語',
    value: 'ja',
  },
  {
    label: 'Deutsch',
    value: 'de',
  },
  {
    label: 'Español',
    value: 'es',
  },
  {
    label: 'Français',
    value: 'fr',
  },
  {
    label: 'Русский язык',
    value: 'ru',
  },
  {
    label: 'Bahasa Melayu',
    value: 'ms',
  },
  {
    label: 'Português',
    value: 'pt',
  },
  {
    label: 'Italiano',
    value: 'it',
  },
  {
    label: 'ภาษาไทย',
    value: 'th',
  },
  {
    label: '简体中文',
    value: 'zh',
  },
]

export default function LandingHeader() {
  const locale =
    typeof window !== 'undefined' ? window.localStorage.getItem('page-locale') || 'en' : 'en'

  const selectedLocale = locale

  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const scrollToSection = (sectionId: string) => {
    if (typeof window !== 'undefined') {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
    setIsMenuOpen(false) // 关闭移动端菜单
  }

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen)

  const setLocale = (locale: string) => {
    if (typeof window !== 'undefined') {
      window.localStorage.setItem('page-locale', locale)
      window.location.reload()
    }
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200">
      <div className="container mx-auto px-4 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/landing" className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-lg flex items-center justify-center">
              <Image
                src="/logo.png"
                alt="EasyGo China Logo"
                width={32}
                height={32}
                className="rounded-lg"
              />
            </div>
            <span className="font-bold text-xl text-gradient-brand">EasyGo China</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <button
              onClick={() => scrollToSection('features')}
              className="text-gray-600 hover:text-primary transition-colors"
            >
              {/* Features */}
              {translations[locale]?.landing.header.features}
            </button>
            <button
              onClick={() => scrollToSection('testimonials')}
              className="text-gray-600 hover:text-primary transition-colors"
            >
              {/* Reviews */}
              {translations[locale]?.landing.header.reviews}
            </button>
            <button
              onClick={() => scrollToSection('story')}
              className="text-gray-600 hover:text-primary transition-colors"
            >
              {/* Our Story */}
              {translations[locale]?.landing.header.story}
            </button>
            <button
              onClick={() => scrollToSection('contact')}
              className="text-gray-600 hover:text-primary transition-colors"
            >
              {/* Contact */}
              {translations[locale]?.landing.header.contact}
            </button>
          </nav>

          {/* Desktop CTA Button */}
          <div className="hidden md:flex items-center">
            <Button
              className="bg-gradient-brand rounded-2xl hover:shadow-brand text-white"
              onClick={() => scrollToSection('download')}
            >
              {/* Download App */}
              {translations[locale]?.landing.header.download}
            </Button>
          </div>

          <div className="hidden md:flex items-center">
            <Select value={selectedLocale} onValueChange={setLocale}>
              <SelectTrigger>
                <SelectValue placeholder="" />
              </SelectTrigger>
              <SelectContent>
                {locales.map((item) => (
                  <SelectItem key={item.value} value={item.value}>
                    {item.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Mobile Menu Button */}
          <button className="md:hidden p-1" onClick={toggleMenu} aria-label="Toggle menu">
            {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 border-t bg-white">
              <button
                className="block w-full text-left px-3 py-2 text-sm font-medium hover:text-primary transition-colors"
                onClick={() => scrollToSection('features')}
              >
                {/* Features */}
                {translations[locale]?.landing.header.features}
              </button>
              <button
                className="block w-full text-left px-3 py-2 text-sm font-medium hover:text-primary transition-colors"
                onClick={() => scrollToSection('testimonials')}
              >
                {/* Reviews */}
                {translations[locale]?.landing.header.reviews}
              </button>
              <button
                className="block w-full text-left px-3 py-2 text-sm font-medium hover:text-primary transition-colors"
                onClick={() => scrollToSection('story')}
              >
                {/* Our Story */}
                {translations[locale]?.landing.header.story}
              </button>
              <button
                className="block w-full text-left px-3 py-2 text-sm font-medium hover:text-primary transition-colors"
                onClick={() => scrollToSection('contact')}
              >
                {/* Contact */}
                {translations[locale]?.landing.header.contact}
              </button>
              <Link
                href="/landing"
                className="block w-full text-left px-3 py-2 text-sm font-medium hover:text-primary transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                {/* Original Site */}
                {translations[locale]?.landing.header.origin}
              </Link>
              <div className="px-3 py-2">
                <Button
                  className="w-full bg-gradient-brand hover:shadow-brand text-white"
                  onClick={() => scrollToSection('download')}
                >
                  {/* Download App */}
                  {translations[locale]?.landing.header.download}
                </Button>
              </div>
              <div className="px-3 py-2">
                <Select value={selectedLocale} onValueChange={setLocale}>
                  <SelectTrigger>
                    <SelectValue placeholder="" />
                  </SelectTrigger>
                  <SelectContent>
                    {locales.map((item) => (
                      <SelectItem key={item.value} value={item.value}>
                        {item.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
