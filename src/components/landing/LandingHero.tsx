'use client'

import Image from 'next/image'
import DownloadButtons from '@/components/DownloadButtons'
import { translations } from '@/languages/translations'

export default function LandingHero() {
  const locale = typeof window !== 'undefined' 
    ? (window.localStorage.getItem('page-locale') || 'en') 
    : 'en'
    
  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-gray-50 to-red-50/30 relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-32 h-32 bg-primary rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-brand-secondary rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="text-center lg:text-left space-y-6">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
              {/* Your Ultimate */}
              {translations[locale]?.landing.hero.title}
              <span className="block text-gradient-brand">
                {/* Self-Guided */}
                {translations[locale]?.landing.hero.title2}
              </span>
              <span className="block">
                {/* Travel Companion */}
                {translations[locale]?.landing.hero.title3}
              </span>
              <span className="block">
                {/* in China */}
                {translations[locale]?.landing.hero.title4}
              </span>
            </h1>

            <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto lg:mx-0">
              {/* Explore China freely with curated routes, local food, and cultural stories – all in
              one app. */}
              {translations[locale]?.landing.hero.subtitle}
            </p>

            {/* Download Buttons */}
            <div className="pt-6">
              <DownloadButtons
                direction="row"
                size="lg"
                containerClassName="justify-center lg:justify-start"
                appStoreUrl="https://apps.apple.com/fr/app/easygo-china-travel-easy-fun/id6743195357"
                googlePlayUrl="https://play.google.com/store/apps/details?id=com.easygochina.app"
              />
            </div>
          </div>

          {/* Right Content - Phone Mockups */}
          <div className="flex justify-center lg:justify-end">
            <div className="flex gap-6 items-center">
              {/* First Phone */}
              <div className="relative">
                {/* Phone Frame */}
                <div className="relative w-56 h-[460px] bg-black rounded-[2.5rem] p-2 shadow-2xl">
                  <div className="w-full h-full bg-white rounded-[2rem] overflow-hidden">
                    <Image
                      src="/apps/app1.jpg"
                      alt="EasyGo China App Screenshot 1"
                      width={224}
                      height={448}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  {/* Phone Details */}
                  <div className="absolute top-5 left-1/2 transform -translate-x-1/2 w-14 h-1 bg-gray-800 rounded-full"></div>
                  <div className="absolute top-7 right-5 w-2 h-2 bg-gray-800 rounded-full"></div>
                </div>

                {/* Floating Elements */}
                <div className="absolute -top-3 -right-3 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center animate-bounce">
                  <span className="text-primary text-lg">🗺️</span>
                </div>
              </div>

              {/* Second Phone - Hidden on mobile */}
              <div className="relative hidden lg:block">
                {/* Phone Frame */}
                <div className="relative w-56 h-[460px] bg-black rounded-[2.5rem] p-2 shadow-2xl">
                  <div className="w-full h-full bg-white rounded-[2rem] overflow-hidden">
                    <Image
                      src="/apps/app3.jpg"
                      alt="EasyGo China App Screenshot 2"
                      width={224}
                      height={448}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  {/* Phone Details */}
                  <div className="absolute top-5 left-1/2 transform -translate-x-1/2 w-14 h-1 bg-gray-800 rounded-full"></div>
                  <div className="absolute top-7 right-5 w-2 h-2 bg-gray-800 rounded-full"></div>
                </div>

                {/* Floating Elements */}
                <div className="absolute -bottom-3 -left-3 w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center animate-bounce [animation-delay:1s]">
                  <span className="text-brand-orange text-lg">🥟</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
