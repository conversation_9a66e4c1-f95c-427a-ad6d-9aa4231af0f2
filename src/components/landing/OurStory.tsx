'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Heart } from 'lucide-react'
import Image from 'next/image'
import { translations } from '@/languages/translations'

export default function OurStory() {
  const locale = typeof window !== 'undefined' 
    ? (window.localStorage.getItem('page-locale') || 'en') 
    : 'en'
  return (
    <section className="py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-red-50/20">
      <div className="container mx-auto px-4 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <Heart className="h-8 w-8 lg:h-12 lg:w-12 text-primary" />
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            {translations[locale]?.landing.ourStory.title}
            {/* Our Story */}
          </h2>
        </div>

        {/* Story Content with Images */}
        <div className="max-w-6xl mx-auto">
          <Card className="bg-white shadow-lg border-0">
            <CardContent className="p-8 lg:p-12">
              <div className="prose md:prose-lg max-w-none text-gray-700 leading-relaxed">
                {/* Opening Story */}
                <div className="grid grid-cols-1 lg:grid-cols-2 md:gap-8 items-center md:mb-12">
                  <div>
                    <p className="text-xl text-gray-800 md:mb-6 font-medium">
                      {/* In the summer of 2024, my wife and I had to cancel plans to host our good
                      friend Janet from Chicago during her trip to Beijing. To make up for it, we
                      wrote her a little travel guide. */}
                      {translations[locale]?.landing.ourStory.story1}
                    </p>
                    <p className="md:mb-6">
                      {/* Well… it started as &quot;just a little something&quot; to help her have a
                      good time — but before we knew it, we&apos;d put together a full-blown
                      handbook. It included everything from must-see sights and cultural tips to
                      transport guides and daily food recommendations. */}
                      {translations[locale]?.landing.ourStory.story11}
                    </p>
                  </div>
                  <div className="relative">
                    <Image
                      src="/photos/1.jpg"
                      alt="Beijing Travel Experience"
                      width={500}
                      height={350}
                      className="w-full h-45 md:h-80 object-cover rounded-xl shadow-lg"
                    />
                  </div>
                </div>

                {/* Food Recommendations Section */}
                <div className="grid grid-cols-1 lg:grid-cols-2 md:gap-8 items-center md:mb-12">
                  <div className="order-2 lg:order-1 relative">
                    <Image
                      src="/photos/2.jpg"
                      alt="Local Food Discovery"
                      width={500}
                      height={350}
                      className="w-full h-45 md:h-80 object-cover rounded-xl shadow-lg"
                    />
                  </div>
                  <div className="order-1 lg:order-2">
                    <p className="md:mb-6">
                      {/* We didn&apos;t want her to worry about meals, so we listed 7–8 restaurants per
                      day, each with 5–7 recommended dishes. We even included ingredients, cooking
                      methods, flavor profiles, and how to eat them — just in case she wasn&apos;t
                      sure what she&apos;d like. We threw in dozens of cafes and bars too. */}
                      {translations[locale]?.landing.ourStory.story2}
                    </p>
                    <div className="bg-red-50 p-6 rounded-xl border-l-4 border-brand-primary">
                      <p className="text-lg font-medium text-red-900 mb-2">
                        {/* &quot;This is better than a tour guide — it&apos;s like you&apos;re right
                        here with me!&quot; */}
                        {translations[locale]?.landing.ourStory.story21}
                      </p>
                      <p className="text-red-700">
                        {/* — Janet, after using our guide for just one day */}
                        — {translations[locale]?.landing.ourStory.story22}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Realization and Expansion */}
                <div className="grid grid-cols-1 lg:grid-cols-2 md:gap-8 items-center md:mb-12">
                  <div>
                    <p className="md:mb-6">
                      {/* That moment made us realize: after 20+ years of travel across 30+ countries,
                      maybe we could help more friends like Janet. */}
                      {translations[locale]?.landing.ourStory.story3}
                    </p>
                    <p>
                      {/* So we spent the next two months building a website and invited Reddit users to
                      test it out. The feedback blew us away:{' '} */}
                      {translations[locale]?.landing.ourStory.story31}
                      <em>
                        {/* &quot;It&apos;s way more accurate than what I found online — saved me hours
                        of research!&quot; */}
                        {translations[locale]?.landing.ourStory.story32}
                      </em>{' '}
                      {/* and{' '} */}
                      {translations[locale]?.landing.ourStory.and}
                      <em>
                        {/* &quot;Beyond dumplings and roast duck, I found so many hidden food
                        gems!&quot; */}
                        {translations[locale]?.landing.ourStory.story33}
                      </em>
                    </p>
                  </div>
                  <div className="relative">
                    <Image
                      src="/photos/3.jpg"
                      alt="Cultural Exploration"
                      width={500}
                      height={350}
                      className="w-full h-60 object-cover rounded-xl shadow-lg"
                    />
                  </div>
                </div>

                {/* App Development */}
                <div className="grid grid-cols-1 lg:grid-cols-2 md:gap-6 md:mb-12 items-center">
                  <div className="relative">
                    <Image
                      src="/photos/5.jpg"
                      alt="Hidden Gems"
                      width={300}
                      height={200}
                      className="w-full h-56 object-cover rounded-xl shadow-lg"
                    />
                  </div>
                  <div>
                    <p className="md:mb-8">
                      {/* Of course, people also mentioned some issues — slow loading, confusing maps,
                      and not enough English support. That&apos;s why we created the EasyGo China
                      app. */}
                      {translations[locale]?.landing.ourStory.story4}
                    </p>
                    <p className="font-medium text-gray-800 md:mb-6">
                      {/* It&apos;s like having a super-knowledgeable local friend in your pocket — at
                      less than the price of a cocktail. */}
                      {translations[locale]?.landing.ourStory.story41}
                    </p>
                  </div>
                </div>

                {/* Features Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 md:gap-6 md:my-8">
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl mb-3">🌐</div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {/* Multi-Language Friendly */}
                      {translations[locale]?.landing.ourStory.featureMap.feature1Title}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {/* Built-in language support system for travelers worldwide */}
                      {translations[locale]?.landing.ourStory.featureMap.feature1Description}
                    </p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl mb-3">🧭</div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {/* 30+ Self-Guided Tours */}
                      {translations[locale]?.landing.ourStory.featureMap.feature2Title}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {/* Covering 11 cities across China */}
                      {translations[locale]?.landing.ourStory.featureMap.feature2Description}
                    </p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl mb-3">❤️</div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {/* 20,000+ Words */}
                      {translations[locale]?.landing.ourStory.featureMap.feature3Title}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {/* Of content per day (some hit 40,000!) */}
                      {translations[locale]?.landing.ourStory.featureMap.feature3Description}
                    </p>
                  </div>
                </div>

                {/* Mission Statement with Images */}
                <div className="bg-green-50 p-8 rounded-xl border-l-4 border-brand-orange md:mb-8">
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 items-center">
                    <div className="lg:col-span-3">
                      <h4 className="text-lg font-bold text-green-900 mb-4">
                        {/* Here&apos;s what we want every traveler in China to enjoy: */}
                        {translations[locale]?.landing.ourStory.statement.title}
                      </h4>
                      <ul className="space-y-3 text-green-800 pl-0">
                        <li className="flex items-start gap-3">
                          <span className="text-primary font-bold">1.</span>
                          <span>
                            <strong className="block">
                              {/* No Chinese? No problem. */}
                              {translations[locale]?.landing.ourStory.statement.title1}
                            </strong> 
                            {/* Easily find dishes you&apos;ll love — even if they have wild names like &quot;Ants
                            Climbing a Tree&quot; or &quot;Husband &amp; Wife Lung Slices.&quot; */}
                            {translations[locale]?.landing.ourStory.statement.description1}
                          </span>
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="text-primary font-bold">2.</span>
                          <span>
                            <strong className="block">
                              {/* No tour groups needed. */}
                              {translations[locale]?.landing.ourStory.statement.title2}
                            </strong>
                            {/* Discover local
                            favorites like foot massages, ear-cleaning, teahouses, and hidden bars —
                            just like the locals do. */}
                            {translations[locale]?.landing.ourStory.statement.description2}
                          </span>
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="text-primary font-bold">3.</span>
                          <span>
                            <strong className="block">
                              {/* No hours of Googling. */}
                              {translations[locale]?.landing.ourStory.statement.title3}
                            </strong> 
                            {/* Just open the
                            app and feel like a friend is showing you around. */}
                            {translations[locale]?.landing.ourStory.statement.description3}
                          </span>
                        </li>
                      </ul>
                    </div>
                    <div className="relative">
                      <Image
                        src="/photos/4.jpg"
                        alt="Travel Experience in China"
                        width={200}
                        height={300}
                        className="w-full h-70 object-cover rounded-lg shadow-md"
                      />
                    </div>
                  </div>
                </div>

                {/* Final Message */}
                <div className="text-center">
                  <p className="text-xl text-gray-800 mb-4 font-medium">
                    {/* After 20 years of DIY travel experience, we built EasyGo China to help you
                    experience the real heartbeat of this amazing country — because the best trips
                    aren&apos;t just about checking off sights. */}
                    {translations[locale]?.landing.ourStory.message.title}
                  </p>
                  <p className="text-xl text-gradient-brand font-bold">
                    {/* They&apos;re about truly understanding the soul of a city. */}
                    {translations[locale]?.landing.ourStory.message.description}
                    </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
