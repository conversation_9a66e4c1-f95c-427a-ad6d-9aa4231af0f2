'use client'

import * as React from 'react'
import { Toaster as Sonner, toast } from 'sonner'

import { useTheme } from '@payloadcms/ui'

export type ToasterProps = React.ComponentProps<typeof Sonner>
function Toaster(props: ToasterProps): React.JSX.Element {
  const { theme = 'system' } = useTheme()
  return (
    <Sonner
      theme={theme as ToasterProps['theme']}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg',
          description: 'group-[.toast]:text-muted-foreground',
          actionButton: 'group-[.toast]:bg-primary group-[.toast]:text-primary-foreground',
          cancelButton: 'group-[.toast]:bg-muted group-[.toast]:text-muted-foreground',
        },
      }}
      {...props}
    />
  )
}
Toaster.displayName = 'Toaster'

export { Toaster, toast }
